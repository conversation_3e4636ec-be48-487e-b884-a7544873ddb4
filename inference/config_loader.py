"""
配置文件加载和验证工具
用于加载和验证推理脚本的配置参数
"""

import yaml
import os
import torch
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = None
        
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 验证配置
            self._validate_config()
            
            # 处理路径
            self._process_paths()
            
            return self.config
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"YAML格式错误: {e}")
    
    def _validate_config(self):
        """验证配置文件的必要字段"""
        required_sections = ['model', 'inference', 'data']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必要部分: {section}")
        
        # 验证模型配置
        model_config = self.config['model']
        required_model_fields = ['base_model_path', 'torch_dtype', 'device_map']
        
        for field in required_model_fields:
            if field not in model_config:
                raise ValueError(f"模型配置缺少必要字段: {field}")
        
        # 验证torch_dtype
        valid_dtypes = ['float32', 'float16', 'bfloat16']
        if model_config['torch_dtype'] not in valid_dtypes:
            raise ValueError(f"不支持的数据类型: {model_config['torch_dtype']}")
    
    def _process_paths(self):
        """处理配置中的路径，转换为绝对路径"""
        # 获取当前工作目录
        base_dir = Path.cwd()
        
        # 处理模型路径
        model_config = self.config['model']
        for path_key in ['base_model_path', 'finetuned_model_path', 'processor_model_path']:
            if path_key in model_config and model_config[path_key]:
                path = Path(model_config[path_key])
                if not path.is_absolute():
                    model_config[path_key] = str(base_dir / path)
        
        # 处理数据路径
        if 'data' in self.config and 'test_images' in self.config['data']:
            test_images = self.config['data']['test_images']
            for image_key in ['base_model_image', 'finetuned_model_image']:
                if image_key in test_images and test_images[image_key]:
                    path = Path(test_images[image_key])
                    if not path.is_absolute():
                        test_images[image_key] = str(base_dir / path)
    
    def get_torch_dtype(self) -> torch.dtype:
        """
        获取PyTorch数据类型
        
        Returns:
            torch.dtype对象
        """
        dtype_str = self.config['model']['torch_dtype']
        dtype_mapping = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16
        }
        return dtype_mapping[dtype_str]
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return self.config['model']
    
    def get_inference_config(self) -> Dict[str, Any]:
        """获取推理配置"""
        return self.config['inference']
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置"""
        return self.config['data']
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置"""
        return self.config.get('output', {})
    
    def setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        if log_config.get('enabled', True):
            level = getattr(logging, log_config.get('level', 'INFO'))
            logging.basicConfig(
                level=level,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )


def load_config_with_overrides(config_path: str = "config.yaml", 
                              overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    加载配置文件并应用覆盖参数
    
    Args:
        config_path: 配置文件路径
        overrides: 覆盖参数字典
        
    Returns:
        最终配置字典
    """
    loader = ConfigLoader(config_path)
    config = loader.load_config()
    
    # 应用覆盖参数
    if overrides:
        config = _deep_update(config, overrides)
    
    return config


def _deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度更新字典
    
    Args:
        base_dict: 基础字典
        update_dict: 更新字典
        
    Returns:
        更新后的字典
    """
    for key, value in update_dict.items():
        if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
            base_dict[key] = _deep_update(base_dict[key], value)
        else:
            base_dict[key] = value
    
    return base_dict
