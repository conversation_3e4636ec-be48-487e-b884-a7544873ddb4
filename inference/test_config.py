"""
配置文件和重构后推理脚本的测试脚本
验证配置加载、参数验证和基本功能
"""

import os
import sys
import tempfile
import yaml
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader, load_config_with_overrides


def test_config_loading():
    """测试配置文件加载功能"""
    print("=" * 50)
    print("测试配置文件加载功能")
    print("=" * 50)
    
    try:
        # 测试加载默认配置文件
        config_path = "config.yaml"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        loader = ConfigLoader(config_path)
        config = loader.load_config()
        
        print("✅ 配置文件加载成功")
        
        # 验证必要的配置部分
        required_sections = ['model', 'inference', 'data']
        for section in required_sections:
            if section in config:
                print(f"✅ 找到配置部分: {section}")
            else:
                print(f"❌ 缺少配置部分: {section}")
                return False
        
        # 验证模型配置
        model_config = config['model']
        print(f"✅ 基础模型路径: {model_config['base_model_path']}")
        print(f"✅ 微调模型路径: {model_config['finetuned_model_path']}")
        print(f"✅ 数据类型: {model_config['torch_dtype']}")
        print(f"✅ 设备映射: {model_config['device_map']}")
        
        # 验证推理配置
        inference_config = config['inference']
        generation_config = inference_config['generation']
        print(f"✅ 最大新token数: {generation_config['max_new_tokens']}")
        print(f"✅ 采样模式: {generation_config['do_sample']}")
        
        # 验证数据配置
        data_config = config['data']
        test_images = data_config['test_images']
        prompts = data_config['prompts']
        print(f"✅ 基础模型测试图像: {test_images['base_model_image']}")
        print(f"✅ 微调模型测试图像: {test_images['finetuned_model_image']}")
        print(f"✅ 基础模型提示词: {prompts['base_model_prompt']}")
        print(f"✅ 微调模型提示词: {prompts['finetuned_model_prompt']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_config_validation():
    """测试配置验证功能"""
    print("\n" + "=" * 50)
    print("测试配置验证功能")
    print("=" * 50)
    
    # 创建临时的无效配置文件
    invalid_configs = [
        # 缺少model部分
        {
            'inference': {'generation': {'max_new_tokens': 100}},
            'data': {'test_images': {}}
        },
        # 无效的torch_dtype
        {
            'model': {
                'base_model_path': '/path/to/model',
                'torch_dtype': 'invalid_dtype',
                'device_map': 'auto'
            },
            'inference': {'generation': {'max_new_tokens': 100}},
            'data': {'test_images': {}}
        }
    ]
    
    for i, invalid_config in enumerate(invalid_configs):
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(invalid_config, f)
                temp_config_path = f.name
            
            loader = ConfigLoader(temp_config_path)
            loader.load_config()
            
            print(f"❌ 测试 {i+1}: 应该检测到无效配置但没有")
            os.unlink(temp_config_path)
            return False
            
        except (ValueError, KeyError) as e:
            print(f"✅ 测试 {i+1}: 正确检测到无效配置: {e}")
            os.unlink(temp_config_path)
        except Exception as e:
            print(f"❌ 测试 {i+1}: 意外错误: {e}")
            os.unlink(temp_config_path)
            return False
    
    return True


def test_config_overrides():
    """测试配置覆盖功能"""
    print("\n" + "=" * 50)
    print("测试配置覆盖功能")
    print("=" * 50)
    
    try:
        config_path = "config.yaml"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 测试覆盖参数
        overrides = {
            'model': {
                'torch_dtype': 'float16'
            },
            'inference': {
                'generation': {
                    'max_new_tokens': 200,
                    'do_sample': True
                }
            }
        }
        
        config = load_config_with_overrides(config_path, overrides)
        
        # 验证覆盖是否生效
        if config['model']['torch_dtype'] == 'float16':
            print("✅ 模型数据类型覆盖成功")
        else:
            print("❌ 模型数据类型覆盖失败")
            return False
        
        if config['inference']['generation']['max_new_tokens'] == 200:
            print("✅ 最大token数覆盖成功")
        else:
            print("❌ 最大token数覆盖失败")
            return False
        
        if config['inference']['generation']['do_sample'] == True:
            print("✅ 采样模式覆盖成功")
        else:
            print("❌ 采样模式覆盖失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置覆盖测试失败: {e}")
        return False


def test_path_processing():
    """测试路径处理功能"""
    print("\n" + "=" * 50)
    print("测试路径处理功能")
    print("=" * 50)
    
    try:
        # 创建包含相对路径的临时配置
        temp_config = {
            'model': {
                'base_model_path': 'models/test_model',
                'torch_dtype': 'bfloat16',
                'device_map': 'auto'
            },
            'inference': {
                'generation': {'max_new_tokens': 100, 'do_sample': False},
                'input_processing': {'skip_special_tokens': True}
            },
            'data': {
                'test_images': {
                    'base_model_image': 'images/test.png'
                },
                'prompts': {
                    'base_model_prompt': 'test prompt'
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(temp_config, f)
            temp_config_path = f.name
        
        loader = ConfigLoader(temp_config_path)
        config = loader.load_config()
        
        # 检查路径是否被转换为绝对路径
        base_model_path = Path(config['model']['base_model_path'])
        test_image_path = Path(config['data']['test_images']['base_model_image'])
        
        if base_model_path.is_absolute():
            print("✅ 模型路径已转换为绝对路径")
        else:
            print("❌ 模型路径未转换为绝对路径")
            os.unlink(temp_config_path)
            return False
        
        if test_image_path.is_absolute():
            print("✅ 图像路径已转换为绝对路径")
        else:
            print("❌ 图像路径未转换为绝对路径")
            os.unlink(temp_config_path)
            return False
        
        os.unlink(temp_config_path)
        return True
        
    except Exception as e:
        print(f"❌ 路径处理测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("开始配置系统测试...")
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("配置验证", test_config_validation),
        ("配置覆盖", test_config_overrides),
        ("路径处理", test_path_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有测试通过！配置系统工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置系统。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
