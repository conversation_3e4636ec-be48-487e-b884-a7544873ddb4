# PaliGemma 推理脚本配置化重构

本项目将原有的硬编码推理脚本重构为基于配置文件的灵活系统，提供更好的可维护性和可扩展性。

## 文件结构

```
vlm_ws/inference/
├── config.yaml                    # 主配置文件
├── config_loader.py               # 配置加载和验证工具
├── inference_paligemma_base.py    # 基础模型推理脚本（重构后）
├── inference_paligemma_lora_ft.py # LoRA微调模型推理脚本（重构后）
├── test_config.py                 # 配置系统测试脚本
└── README.md                      # 本文档
```

## 主要改进

### 1. 配置文件管理
- **统一配置**: 所有参数集中在 `config.yaml` 文件中
- **层次结构**: 清晰的配置组织，包括模型、推理、数据和输出配置
- **参数验证**: 自动验证配置文件的完整性和有效性
- **路径处理**: 自动处理相对路径和绝对路径转换

### 2. 灵活的参数覆盖
- **命令行参数**: 支持通过命令行指定自定义图像和提示词
- **配置覆盖**: 支持程序化覆盖配置参数
- **多环境支持**: 可以为不同环境使用不同的配置文件

### 3. 增强的错误处理
- **文件存在性检查**: 验证模型文件和图像文件是否存在
- **参数验证**: 验证数据类型、设备配置等参数的有效性
- **详细日志**: 提供详细的执行日志和错误信息

### 4. 可扩展性
- **模块化设计**: 配置加载、模型加载、推理执行分离
- **易于扩展**: 可以轻松添加新的配置选项和功能
- **代码复用**: 通用的配置加载器可用于其他脚本

## 配置文件说明

### 模型配置 (model)
```yaml
model:
  base_model_path: "/path/to/base/model"      # 基础模型路径
  finetuned_model_path: "/path/to/ft/model"   # 微调模型路径
  processor_model_path: "/path/to/processor"  # 处理器模型路径
  torch_dtype: "bfloat16"                     # 数据类型
  device_map: "auto"                          # 设备映射
```

### 推理配置 (inference)
```yaml
inference:
  generation:
    max_new_tokens: 100        # 最大生成token数
    do_sample: false           # 是否使用采样
    temperature: 1.0           # 采样温度
    top_p: 0.9                # Top-p采样
    top_k: 50                 # Top-k采样
  input_processing:
    skip_special_tokens: true  # 解码时跳过特殊token
```

### 数据配置 (data)
```yaml
data:
  test_images:
    base_model_image: "path/to/base/image.png"
    finetuned_model_image: "path/to/ft/image.png"
  prompts:
    base_model_prompt: "Your prompt here"
    finetuned_model_prompt: "Your prompt here"
    custom_prompt: ""  # 自定义提示词
```

## 使用方法

### 1. 基础模型推理
```bash
# 使用默认配置
python inference_paligemma_base.py

# 使用自定义配置文件
python inference_paligemma_base.py --config custom_config.yaml

# 使用自定义图像和提示词
python inference_paligemma_base.py --image path/to/image.png --prompt "Your custom prompt"
```

### 2. LoRA微调模型推理
```bash
# 使用默认配置
python inference_paligemma_lora_ft.py

# 使用自定义配置文件
python inference_paligemma_lora_ft.py --config custom_config.yaml

# 使用自定义图像和提示词
python inference_paligemma_lora_ft.py --image path/to/image.png --prompt "Your custom prompt"
```

### 3. 配置系统测试
```bash
# 运行配置系统测试
python test_config.py
```

## 配置参数详解

### 支持的数据类型
- `float32`: 32位浮点数
- `float16`: 16位浮点数
- `bfloat16`: Brain Float 16位（推荐）

### 设备映射选项
- `"auto"`: 自动选择设备
- `"cpu"`: 强制使用CPU
- `"cuda"`: 使用GPU
- `"cuda:0"`: 使用特定GPU

### 生成参数
- `max_new_tokens`: 控制生成文本的最大长度
- `do_sample`: 
  - `false`: 使用贪婪解码（确定性）
  - `true`: 使用采样（随机性）
- `temperature`: 控制采样的随机性（仅在do_sample=true时有效）
- `top_p`: 核采样参数
- `top_k`: Top-k采样参数

## 错误处理

脚本包含完善的错误处理机制：

1. **配置文件错误**: 检查配置文件是否存在和格式是否正确
2. **参数验证错误**: 验证必要参数是否存在和有效
3. **文件不存在错误**: 检查模型文件和图像文件是否存在
4. **模型加载错误**: 处理模型加载过程中的错误
5. **推理错误**: 处理推理过程中的错误

## 日志配置

可以通过配置文件控制日志输出：

```yaml
logging:
  enabled: true          # 是否启用日志
  level: "INFO"         # 日志级别: DEBUG, INFO, WARNING, ERROR
```

## 输出配置

支持灵活的输出配置：

```yaml
output:
  verbose: true                    # 是否显示详细输出
  format: "text"                  # 输出格式: text, json
  save_results: false             # 是否保存结果到文件
  results_path: "results.txt"     # 结果保存路径
```

## 扩展指南

### 添加新的配置选项
1. 在 `config.yaml` 中添加新的配置项
2. 在 `config_loader.py` 中添加相应的验证逻辑
3. 在推理脚本中使用新的配置项

### 添加新的推理脚本
1. 复制现有的推理脚本作为模板
2. 修改模型加载和推理逻辑
3. 确保使用统一的配置加载机制

## 测试

运行测试脚本验证配置系统：

```bash
python test_config.py
```

测试包括：
- 配置文件加载测试
- 配置验证测试
- 配置覆盖测试
- 路径处理测试

## 故障排除

### 常见问题

1. **配置文件不存在**
   - 确保 `config.yaml` 文件在正确的位置
   - 检查配置文件路径是否正确

2. **模型文件不存在**
   - 检查配置文件中的模型路径是否正确
   - 确保模型文件已下载并放在正确位置

3. **图像文件不存在**
   - 检查配置文件中的图像路径是否正确
   - 确保测试图像文件存在

4. **内存不足**
   - 调整 `torch_dtype` 为 `float16` 或 `bfloat16`
   - 使用适当的 `device_map` 配置

5. **CUDA错误**
   - 检查CUDA是否正确安装
   - 尝试使用 `device_map: "cpu"` 进行CPU推理

## 性能优化建议

1. **使用合适的数据类型**: `bfloat16` 通常提供最好的性能和精度平衡
2. **设备映射**: 使用 `"auto"` 让系统自动选择最优设备配置
3. **批处理**: 对于多个图像，考虑实现批处理推理
4. **缓存**: 考虑缓存已加载的模型以避免重复加载
