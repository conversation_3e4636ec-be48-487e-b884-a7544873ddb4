# PaliGemma 推理配置文件
# 此配置文件包含推理脚本的所有可配置参数

# 模型配置
model:
  # 基础模型路径 - 用于加载处理器和基础推理
  base_model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-mix-448"
  
  # 微调模型路径 - 用于LoRA微调后的模型推理
  finetuned_model_path: "/home/<USER>/johnny_ws/vlm_ws/fine_tune/paligemma_custom_finetuned_448_720"
  
  # 处理器模型路径 - 通常与基础模型相同，用于加载处理器
  processor_model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-448"
  
  # 模型数据类型 - 支持的类型: float32, float16, bfloat16
  torch_dtype: "bfloat16"
  
  # 设备映射策略 - "auto", "cpu", "cuda", 或具体的设备ID如"cuda:0"
  device_map: "auto"

# 推理配置
inference:
  # 生成参数
  generation:
    # 最大新生成token数量
    max_new_tokens: 100
    
    # 是否使用采样生成 - False为贪婪解码，True为采样
    do_sample: false
    
    # 采样温度 - 仅在do_sample=True时生效
    temperature: 1.0
    
    # Top-p采样参数 - 仅在do_sample=True时生效
    top_p: 0.9
    
    # Top-k采样参数 - 仅在do_sample=True时生效
    top_k: 50
  
  # 输入处理配置
  input_processing:
    # 是否跳过特殊token在解码时
    skip_special_tokens: true

# 数据配置
data:
  # 测试图像配置
  test_images:
    # 基础模型测试图像路径
    base_model_image: "/home/<USER>/johnny_ws/datasets/action_images/place_ok.png"

    # 微调模型测试图像路径
    finetuned_model_image: "/home/<USER>/johnny_ws/datasets/augmented_data/images/test/terminal1_1a.png"
  
  # 提示词配置
  prompts:
    # 基础模型提示词
    # base_model_prompt: "<image>The rule is the OK product go to the blue box, the NOK product go to the yellow box, based on the woman's behaviour, is this porduct OK or NOK? answer OK or NOK to me."
    # 微调模型提示词
    finetuned_model_prompt: "<image>Are there any defects in the terminal connection? Describe the defects in detail."
    
    # 自定义提示词 - 可以通过命令行参数覆盖
    custom_prompt: ""

# 输出配置
output:
  # 是否显示详细输出
  verbose: true
  
  # 输出格式 - "text", "json"
  format: "text"
  
  # 是否保存结果到文件
  save_results: false
  
  # 结果保存路径
  results_path: "inference_results.txt"

# 日志配置
logging:
  # 日志级别 - DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # 是否启用日志
  enabled: true
