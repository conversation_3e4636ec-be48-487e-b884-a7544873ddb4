"""
PaliGemma LoRA微调模型推理脚本
使用配置文件进行参数管理，支持灵活的微调模型推理配置
"""

from transformers import PaliGemmaProcessor, PaliGemmaForConditionalGeneration
import torch
import argparse
import logging
import os
from transformers.image_utils import load_image
from config_loader import load_config_with_overrides


def setup_logging(config):
    """设置日志配置"""
    log_config = config.get('logging', {})

    if log_config.get('enabled', True):
        level = getattr(logging, log_config.get('level', 'INFO'))
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )


def load_model_and_processor(config):
    """
    加载微调模型和处理器

    Args:
        config: 配置字典

    Returns:
        tuple: (model, processor)
    """
    model_config = config['model']

    # 获取模型路径
    processor_model_path = model_config.get('processor_model_path', model_config['base_model_path'])
    finetuned_model_path = model_config['finetuned_model_path']

    # 获取torch数据类型
    dtype_str = model_config['torch_dtype']
    dtype_mapping = {
        'float32': torch.float32,
        'float16': torch.float16,
        'bfloat16': torch.bfloat16
    }
    torch_dtype = dtype_mapping[dtype_str]

    logging.info(f"加载处理器模型: {processor_model_path}")
    logging.info(f"加载微调模型: {finetuned_model_path}")
    logging.info(f"使用数据类型: {dtype_str}")
    logging.info(f"设备映射: {model_config['device_map']}")

    # 加载处理器（使用基础模型）
    processor = PaliGemmaProcessor.from_pretrained(processor_model_path)

    # 加载微调后的模型
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        finetuned_model_path,
        torch_dtype=torch_dtype,
        device_map=model_config['device_map']
    ).eval()

    return model, processor


def run_inference(model, processor, config, custom_image_path=None, custom_prompt=None):
    """
    执行推理

    Args:
        model: 加载的模型
        processor: 处理器
        config: 配置字典
        custom_image_path: 自定义图像路径
        custom_prompt: 自定义提示词

    Returns:
        str: 生成的文本
    """
    data_config = config['data']
    inference_config = config['inference']

    # 确定图像路径和提示词
    image_path = custom_image_path or data_config['test_images']['finetuned_model_image']
    prompt = custom_prompt or data_config['prompts']['finetuned_model_prompt']

    # 如果有自定义提示词配置，优先使用
    if not custom_prompt and data_config['prompts'].get('custom_prompt'):
        prompt = data_config['prompts']['custom_prompt']

    logging.info(f"加载测试图像: {image_path}")
    logging.info(f"使用提示词: {prompt}")

    # 检查图像文件是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图像文件不存在: {image_path}")

    # 加载图像
    image = load_image(image_path)

    # 准备输入
    torch_dtype = torch.bfloat16 if config['model']['torch_dtype'] == 'bfloat16' else torch.float16
    model_inputs = processor(text=prompt, images=image, return_tensors="pt").to(torch_dtype).to(model.device)

    # 获取生成参数
    generation_config = inference_config['generation']

    logging.info("开始生成回答...")

    # 生成回答
    input_len = model_inputs["input_ids"].shape[-1]
    generated_ids = model.generate(
        **model_inputs,
        max_new_tokens=generation_config['max_new_tokens'],
        do_sample=generation_config['do_sample'],
        temperature=generation_config.get('temperature', 1.0) if generation_config['do_sample'] else None,
        top_p=generation_config.get('top_p', 0.9) if generation_config['do_sample'] else None,
        top_k=generation_config.get('top_k', 50) if generation_config['do_sample'] else None,
    )

    # 解码输出
    skip_special_tokens = inference_config['input_processing']['skip_special_tokens']
    generated_text = processor.batch_decode(generated_ids[:, input_len:], skip_special_tokens=skip_special_tokens)[0]

    return generated_text


def save_results(result, config):
    """保存推理结果"""
    output_config = config.get('output', {})

    if output_config.get('save_results', False):
        results_path = output_config.get('results_path', 'inference_results_finetuned.txt')

        with open(results_path, 'w', encoding='utf-8') as f:
            f.write(f"微调模型推理结果:\n{result}\n")

        logging.info(f"结果已保存到: {results_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma LoRA微调模型推理')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--image', type=str, help='自定义图像路径')
    parser.add_argument('--prompt', type=str, help='自定义提示词')

    args = parser.parse_args()

    try:
        # 加载配置
        config = load_config_with_overrides(args.config)

        # 设置日志
        setup_logging(config)

        logging.info("开始PaliGemma LoRA微调模型推理")

        # 加载模型和处理器
        model, processor = load_model_and_processor(config)

        # 执行推理
        result = run_inference(model, processor, config, args.image, args.prompt)

        # 输出结果
        output_config = config.get('output', {})
        if output_config.get('verbose', True):
            print(f"Generated answer: {result}")

        # 保存结果
        save_results(result, config)

        logging.info("推理完成")

    except Exception as e:
        logging.error(f"推理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()