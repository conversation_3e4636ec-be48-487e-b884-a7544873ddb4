#!/usr/bin/env python3
"""
Standalone runner for PaliGemma2 evaluation benchmark
This script resolves import issues by setting up the Python path correctly.
Author: <PERSON>
"""

import sys
import os
from pathlib import Path

# Add the current directory and evaluation_benchmark to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "evaluation_benchmark"))

# Now import and run the main evaluation
if __name__ == "__main__":
    from evaluation_benchmark.main_evaluation import main
    main()
