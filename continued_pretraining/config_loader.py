#!/usr/bin/env python3
"""
Configuration loader for PaliGemma continued pre-training

This module provides utilities to load and validate configuration files
for the continued pre-training process.

Author: Johnny
Date: 2025-07-15
"""

import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)

@dataclass
class ContinuedPretrainingConfigFromYAML:
    """Configuration class that can be loaded from YAML file"""
    
    # Model and data paths
    model_path: str = "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224"
    output_dir: str = "./paligemma_continued_pretraining_captioning"
    dataset_name: str = "nlphuji/flickr30k"
    
    # Training hyperparameters
    num_train_epochs: int = 3
    per_device_train_batch_size: int = 2
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 8
    learning_rate: float = 5e-6
    weight_decay: float = 0.01
    warmup_ratio: float = 0.1
    max_grad_norm: float = 1.0
    
    # LoRA configuration
    use_lora: bool = True
    lora_r: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    lora_target_modules: list = field(default_factory=lambda: [
        "q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"
    ])
    
    # Monitoring and checkpointing
    save_steps: int = 500
    eval_steps: int = 500
    logging_steps: int = 50
    save_total_limit: int = 3
    evaluation_strategy: str = "steps"
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_loss"
    
    # Optimization
    dataloader_pin_memory: bool = False
    dataloader_num_workers: int = 4
    remove_unused_columns: bool = False
    bf16: bool = True
    tf32: bool = True
    optim: str = "adamw_torch_fused"
    
    # Logging
    report_to: list = field(default_factory=lambda: ["tensorboard"])
    run_name: str = "paligemma_continued_pretraining"
    project_name: str = "paligemma-continued-pretraining"
    
    # Dataset settings
    max_length: int = 512
    train_split_ratio: float = 0.9
    validation_split_ratio: float = 0.1
    
    # Evaluation settings
    num_eval_samples: int = 100
    generation_max_new_tokens: int = 100
    generation_do_sample: bool = False
    generation_temperature: float = 0.0
    
    # Advanced settings
    dataloader_drop_last: bool = True
    ignore_data_skip: bool = True
    resume_from_checkpoint: Optional[str] = None
    
    # Continued pre-training specific
    train_vision_tower: bool = False
    train_multimodal_projector: bool = True
    train_language_model: bool = True
    lr_scheduler_type: str = "cosine"
    caption_prompt_template: str = "Describe this image:"
    caption_max_length: int = 100

def load_config_from_yaml(config_path: str) -> ContinuedPretrainingConfigFromYAML:
    """
    Load configuration from YAML file
    
    Args:
        config_path: Path to the YAML configuration file
        
    Returns:
        ContinuedPretrainingConfigFromYAML object
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        yaml_config = yaml.safe_load(f)
    
    # Flatten nested configuration
    config_dict = {}
    
    # Extract model configuration
    if 'model' in yaml_config:
        config_dict.update(yaml_config['model'])
    
    # Extract training configuration
    if 'training' in yaml_config:
        config_dict.update(yaml_config['training'])
    
    # Extract LoRA configuration
    if 'lora' in yaml_config:
        lora_config = yaml_config['lora']
        config_dict['use_lora'] = lora_config.get('use_lora', True)
        config_dict['lora_r'] = lora_config.get('lora_r', 16)
        config_dict['lora_alpha'] = lora_config.get('lora_alpha', 32)
        config_dict['lora_dropout'] = lora_config.get('lora_dropout', 0.1)
        config_dict['lora_target_modules'] = lora_config.get('target_modules', [
            "q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"
        ])
    
    # Extract monitoring configuration
    if 'monitoring' in yaml_config:
        config_dict.update(yaml_config['monitoring'])
    
    # Extract optimization configuration
    if 'optimization' in yaml_config:
        config_dict.update(yaml_config['optimization'])
    
    # Extract logging configuration
    if 'logging' in yaml_config:
        config_dict.update(yaml_config['logging'])
    
    # Extract dataset configuration
    if 'dataset' in yaml_config:
        config_dict.update(yaml_config['dataset'])
    
    # Extract evaluation configuration
    if 'evaluation' in yaml_config:
        config_dict.update(yaml_config['evaluation'])
    
    # Extract advanced configuration
    if 'advanced' in yaml_config:
        config_dict.update(yaml_config['advanced'])
    
    # Extract continued pre-training specific configuration
    if 'continued_pretraining' in yaml_config:
        config_dict.update(yaml_config['continued_pretraining'])
    
    # Create configuration object
    config = ContinuedPretrainingConfigFromYAML()
    
    # Update configuration with loaded values, with type conversion
    for key, value in config_dict.items():
        if hasattr(config, key):
            # Get the expected type from the default value
            default_value = getattr(config, key)
            if default_value is not None:
                expected_type = type(default_value)
                try:
                    # Convert value to expected type
                    if expected_type == float and isinstance(value, (int, str)):
                        value = float(value)
                    elif expected_type == int and isinstance(value, (float, str)):
                        value = int(value)
                    elif expected_type == bool and isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    setattr(config, key, value)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not convert {key}={value} to {expected_type}: {e}")
            else:
                setattr(config, key, value)
        else:
            logger.warning(f"Unknown configuration key: {key}")
    
    return config

def validate_config(config: ContinuedPretrainingConfigFromYAML) -> bool:
    """
    Validate configuration settings
    
    Args:
        config: Configuration object to validate
        
    Returns:
        True if configuration is valid, False otherwise
    """
    errors = []
    
    # Validate model path
    if not os.path.exists(config.model_path):
        errors.append(f"Model path does not exist: {config.model_path}")
    
    # Validate learning rate
    if config.learning_rate <= 0 or config.learning_rate > 1:
        errors.append(f"Invalid learning rate: {config.learning_rate}")
    
    # Validate batch sizes
    if config.per_device_train_batch_size <= 0:
        errors.append(f"Invalid train batch size: {config.per_device_train_batch_size}")
    
    if config.per_device_eval_batch_size <= 0:
        errors.append(f"Invalid eval batch size: {config.per_device_eval_batch_size}")
    
    # Validate epochs
    if config.num_train_epochs <= 0:
        errors.append(f"Invalid number of epochs: {config.num_train_epochs}")
    
    # Validate LoRA settings
    if config.use_lora:
        if config.lora_r <= 0:
            errors.append(f"Invalid LoRA rank: {config.lora_r}")
        if config.lora_alpha <= 0:
            errors.append(f"Invalid LoRA alpha: {config.lora_alpha}")
        if not (0 <= config.lora_dropout <= 1):
            errors.append(f"Invalid LoRA dropout: {config.lora_dropout}")
    
    # Validate split ratios
    if not (0 < config.train_split_ratio < 1):
        errors.append(f"Invalid train split ratio: {config.train_split_ratio}")
    
    if not (0 < config.validation_split_ratio < 1):
        errors.append(f"Invalid validation split ratio: {config.validation_split_ratio}")
    
    if config.train_split_ratio + config.validation_split_ratio > 1:
        errors.append("Train and validation split ratios sum to more than 1")
    
    # Log errors
    if errors:
        logger.error("Configuration validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False
    
    logger.info("Configuration validation passed")
    return True

def save_config_to_yaml(config: ContinuedPretrainingConfigFromYAML, output_path: str):
    """
    Save configuration to YAML file
    
    Args:
        config: Configuration object to save
        output_path: Path to save the YAML file
    """
    config_dict = {
        'model': {
            'model_path': config.model_path,
            'output_dir': config.output_dir,
            'dataset_name': config.dataset_name,
        },
        'training': {
            'num_train_epochs': config.num_train_epochs,
            'per_device_train_batch_size': config.per_device_train_batch_size,
            'per_device_eval_batch_size': config.per_device_eval_batch_size,
            'gradient_accumulation_steps': config.gradient_accumulation_steps,
            'learning_rate': config.learning_rate,
            'weight_decay': config.weight_decay,
            'warmup_ratio': config.warmup_ratio,
            'max_grad_norm': config.max_grad_norm,
        },
        'lora': {
            'use_lora': config.use_lora,
            'lora_r': config.lora_r,
            'lora_alpha': config.lora_alpha,
            'lora_dropout': config.lora_dropout,
            'target_modules': config.lora_target_modules,
        },
        'monitoring': {
            'save_steps': config.save_steps,
            'eval_steps': config.eval_steps,
            'logging_steps': config.logging_steps,
            'save_total_limit': config.save_total_limit,
            'evaluation_strategy': config.evaluation_strategy,
            'load_best_model_at_end': config.load_best_model_at_end,
            'metric_for_best_model': config.metric_for_best_model,
        },
        'optimization': {
            'dataloader_pin_memory': config.dataloader_pin_memory,
            'dataloader_num_workers': config.dataloader_num_workers,
            'remove_unused_columns': config.remove_unused_columns,
            'bf16': config.bf16,
            'tf32': config.tf32,
            'optim': config.optim,
        },
        'logging': {
            'report_to': config.report_to,
            'run_name': config.run_name,
            'project_name': config.project_name,
        },
        'continued_pretraining': {
            'train_vision_tower': config.train_vision_tower,
            'train_multimodal_projector': config.train_multimodal_projector,
            'train_language_model': config.train_language_model,
            'lr_scheduler_type': config.lr_scheduler_type,
            'caption_prompt_template': config.caption_prompt_template,
            'caption_max_length': config.caption_max_length,
        }
    }
    
    with open(output_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    logger.info(f"Configuration saved to {output_path}")

# Example usage
if __name__ == "__main__":
    # Load configuration from YAML
    config = load_config_from_yaml("config_continued_pretraining.yaml")
    
    # Validate configuration
    if validate_config(config):
        print("✅ Configuration is valid!")
        print(f"Model path: {config.model_path}")
        print(f"Learning rate: {config.learning_rate}")
        print(f"Use LoRA: {config.use_lora}")
    else:
        print("❌ Configuration validation failed!")
