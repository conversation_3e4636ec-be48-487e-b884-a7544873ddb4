#!/usr/bin/env python3
"""
Continued Pre-training Script for PaliGemma on Image Captioning Domain

This script implements continued pre-training (also known as continual pre-training) 
for PaliGemma model specifically adapted for image captioning tasks. 

Key differences from standard fine-tuning:
- Lower learning rates optimized for domain adaptation
- Longer training schedules with careful warmup
- Focus on general domain knowledge rather than task-specific performance
- Comprehensive memory management for GPU efficiency

Author: Johnny
Date: 2025-07-15
"""

import os
import sys
import gc
import logging
import argparse
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json
from pathlib import Path

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from transformers import (
    PaliGemmaProcessor, 
    PaliGemmaForConditionalGeneration,
    TrainingArguments,
    Trainer,
    get_cosine_schedule_with_warmup,
    get_linear_schedule_with_warmup
)
from transformers.trainer_callback import TrainerCallback
from peft import get_peft_model, LoraConfig, TaskType
from datasets import load_dataset, Dataset as HFDataset
from PIL import Image
import numpy as np
from tqdm import tqdm
import wandb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('continued_pretraining.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ContinuedPretrainingConfig:
    """Configuration for continued pre-training"""
    # Model and data paths
    model_path: str = "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224"
    output_dir: str = "./paligemma_continued_pretraining_captioning"
    dataset_name: str = "nlphuji/flickr30k"  # Default captioning dataset
    
    # Training hyperparameters optimized for continued pre-training
    num_train_epochs: int = 3
    per_device_train_batch_size: int = 2
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 8
    learning_rate: float = 5e-6  # Lower than fine-tuning (typically 2e-5)
    weight_decay: float = 0.01
    warmup_ratio: float = 0.1  # Longer warmup for stability
    max_grad_norm: float = 1.0
    
    # LoRA configuration for memory efficiency
    use_lora: bool = True
    lora_r: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    
    # Training stability and monitoring
    save_steps: int = 500
    eval_steps: int = 500
    logging_steps: int = 50
    save_total_limit: int = 3
    evaluation_strategy: str = "steps"
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_loss"
    
    # Memory management
    dataloader_pin_memory: bool = False
    dataloader_num_workers: int = 4
    remove_unused_columns: bool = False
    
    # Mixed precision and optimization
    bf16: bool = True
    tf32: bool = True
    optim: str = "adamw_torch_fused"
    
    # Monitoring
    report_to: List[str] = None
    run_name: str = "paligemma_continued_pretraining"
    
    def __post_init__(self):
        if self.report_to is None:
            self.report_to = ["tensorboard"]

class ImageCaptioningDataset(Dataset):
    """Dataset class for image captioning with continued pre-training format"""
    
    def __init__(self, dataset: HFDataset, processor: PaliGemmaProcessor, max_length: int = 512):
        self.dataset = dataset
        self.processor = processor
        self.max_length = max_length
        
    def __len__(self):
        return len(self.dataset)
    
    def __getitem__(self, idx):
        try:
            item = self.dataset[idx]
            
            # Handle different dataset formats
            if 'image' in item:
                image = item['image']
            elif 'img' in item:
                image = item['img']
            else:
                raise KeyError("No image field found in dataset")
                
            # Handle caption field variations
            if 'caption' in item:
                caption = item['caption']
            elif 'text' in item:
                caption = item['text']
            elif 'captions' in item and isinstance(item['captions'], list):
                caption = item['captions'][0]  # Take first caption
            else:
                raise KeyError("No caption field found in dataset")
            
            # Ensure image is PIL Image
            if not isinstance(image, Image.Image):
                image = Image.fromarray(image).convert('RGB')
            else:
                image = image.convert('RGB')
            
            # Format for continued pre-training (simple captioning task)
            prompt = "Describe this image:"
            
            return {
                'image': image,
                'prompt': prompt,
                'caption': str(caption)
            }
            
        except Exception as e:
            logger.warning(f"Error processing item {idx}: {e}")
            # Return a dummy item to avoid training interruption
            dummy_image = Image.new('RGB', (224, 224), color='white')
            return {
                'image': dummy_image,
                'prompt': "Describe this image:",
                'caption': "A white image."
            }

def collate_fn(batch: List[Dict], processor: PaliGemmaProcessor, device: torch.device):
    """Custom collate function for continued pre-training"""
    images = [item['image'] for item in batch]
    prompts = [item['prompt'] for item in batch]
    captions = [item['caption'] for item in batch]
    
    # Process inputs
    inputs = processor(
        text=prompts,
        images=images,
        suffix=captions,
        return_tensors="pt",
        padding="longest",
        truncation=True,
        max_length=512
    )
    
    # Move to device and convert to appropriate dtype
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    return inputs

class MemoryCallback(TrainerCallback):
    """Callback for monitoring and managing GPU memory"""
    
    def on_step_end(self, args, state, control, **kwargs):
        if state.global_step % 100 == 0:
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / 1024**3
                memory_reserved = torch.cuda.memory_reserved() / 1024**3
                logger.info(f"Step {state.global_step}: GPU Memory - Allocated: {memory_allocated:.2f}GB, Reserved: {memory_reserved:.2f}GB")
    
    def on_epoch_end(self, args, state, control, **kwargs):
        # Force garbage collection at epoch end
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("Cleared GPU cache at epoch end")

def clear_gpu_memory():
    """Utility function to clear GPU memory"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def setup_model_and_processor(config: ContinuedPretrainingConfig) -> Tuple[nn.Module, PaliGemmaProcessor]:
    """Setup model and processor with memory optimization"""
    logger.info(f"Loading model from {config.model_path}")
    
    # Clear memory before loading
    clear_gpu_memory()
    
    # Load processor
    processor = PaliGemmaProcessor.from_pretrained(config.model_path)
    
    # Load model with memory optimization
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        config.model_path,
        torch_dtype=torch.bfloat16 if config.bf16 else torch.float32,
        device_map="auto",
        trust_remote_code=True
    )
    
    # For continued pre-training, we typically want to train more components
    # than in task-specific fine-tuning, but still freeze vision encoder for stability
    for param in model.vision_tower.parameters():
        param.requires_grad = False
    
    # Keep multi-modal projector trainable for domain adaptation
    for param in model.multi_modal_projector.parameters():
        param.requires_grad = True
    
    # Apply LoRA if specified
    if config.use_lora:
        logger.info("Applying LoRA configuration")
        lora_config = LoraConfig(
            r=config.lora_r,
            lora_alpha=config.lora_alpha,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
            lora_dropout=config.lora_dropout,
            task_type=TaskType.CAUSAL_LM,
        )
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
    
    return model, processor

def load_and_prepare_dataset(config: ContinuedPretrainingConfig, processor: PaliGemmaProcessor):
    """Load and prepare dataset for continued pre-training"""
    logger.info(f"Loading dataset: {config.dataset_name}")
    
    try:
        # Load dataset
        if config.dataset_name == "nlphuji/flickr30k":
            dataset = load_dataset(config.dataset_name, split="test")  # Use test split for demo
            # For production, you'd want to use train split and create your own validation split
        else:
            dataset = load_dataset(config.dataset_name)
            if isinstance(dataset, dict):
                dataset = dataset['train']  # Take train split
        
        # Create train/validation split for continued pre-training
        dataset = dataset.train_test_split(test_size=0.1, seed=42)
        train_dataset = dataset['train']
        eval_dataset = dataset['test']
        
        logger.info(f"Train dataset size: {len(train_dataset)}")
        logger.info(f"Eval dataset size: {len(eval_dataset)}")
        
        # Wrap in custom dataset class
        train_dataset = ImageCaptioningDataset(train_dataset, processor)
        eval_dataset = ImageCaptioningDataset(eval_dataset, processor)
        
        return train_dataset, eval_dataset
        
    except Exception as e:
        logger.error(f"Error loading dataset: {e}")
        raise

def create_training_arguments(config: ContinuedPretrainingConfig) -> TrainingArguments:
    """Create training arguments optimized for continued pre-training"""
    return TrainingArguments(
        output_dir=config.output_dir,
        num_train_epochs=config.num_train_epochs,
        per_device_train_batch_size=config.per_device_train_batch_size,
        per_device_eval_batch_size=config.per_device_eval_batch_size,
        gradient_accumulation_steps=config.gradient_accumulation_steps,
        learning_rate=config.learning_rate,
        weight_decay=config.weight_decay,
        warmup_ratio=config.warmup_ratio,
        max_grad_norm=config.max_grad_norm,
        
        # Evaluation and saving
        evaluation_strategy=config.evaluation_strategy,
        eval_steps=config.eval_steps,
        save_strategy="steps",
        save_steps=config.save_steps,
        save_total_limit=config.save_total_limit,
        load_best_model_at_end=config.load_best_model_at_end,
        metric_for_best_model=config.metric_for_best_model,
        
        # Logging and monitoring
        logging_steps=config.logging_steps,
        report_to=config.report_to,
        run_name=config.run_name,
        
        # Optimization
        optim=config.optim,
        bf16=config.bf16,
        tf32=config.tf32,
        
        # Data loading
        dataloader_pin_memory=config.dataloader_pin_memory,
        dataloader_num_workers=config.dataloader_num_workers,
        remove_unused_columns=config.remove_unused_columns,
        
        # Stability
        dataloader_drop_last=True,
        ignore_data_skip=True,
    )

def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description="Continued Pre-training for PaliGemma on Image Captioning")
    parser.add_argument("--config", type=str, default=None,
                       help="Path to YAML configuration file")
    parser.add_argument("--model_path", type=str, default="/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224",
                       help="Path to the base PaliGemma model")
    parser.add_argument("--output_dir", type=str, default="./paligemma_continued_pretraining_captioning",
                       help="Output directory for checkpoints")
    parser.add_argument("--dataset_name", type=str, default="nlphuji/flickr30k",
                       help="Dataset name for continued pre-training")
    parser.add_argument("--learning_rate", type=float, default=5e-6,
                       help="Learning rate for continued pre-training")
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=2,
                       help="Per-device batch size")
    parser.add_argument("--use_lora", action="store_true", default=True,
                       help="Use LoRA for parameter-efficient training")
    parser.add_argument("--use_wandb", action="store_true",
                       help="Use Weights & Biases for logging")
    parser.add_argument("--resume_from_checkpoint", type=str, default=None,
                       help="Path to checkpoint to resume from")

    args = parser.parse_args()

    # Load configuration from YAML if provided
    if args.config:
        try:
            from config_loader import load_config_from_yaml, validate_config
            yaml_config = load_config_from_yaml(args.config)
            if not validate_config(yaml_config):
                logger.error("Configuration validation failed")
                sys.exit(1)

            # Create configuration from YAML
            config = ContinuedPretrainingConfig(
                model_path=yaml_config.model_path,
                output_dir=yaml_config.output_dir,
                dataset_name=yaml_config.dataset_name,
                learning_rate=yaml_config.learning_rate,
                num_train_epochs=yaml_config.num_train_epochs,
                per_device_train_batch_size=yaml_config.per_device_train_batch_size,
                per_device_eval_batch_size=yaml_config.per_device_eval_batch_size,
                gradient_accumulation_steps=yaml_config.gradient_accumulation_steps,
                weight_decay=yaml_config.weight_decay,
                warmup_ratio=yaml_config.warmup_ratio,
                max_grad_norm=yaml_config.max_grad_norm,
                use_lora=yaml_config.use_lora,
                lora_r=yaml_config.lora_r,
                lora_alpha=yaml_config.lora_alpha,
                lora_dropout=yaml_config.lora_dropout,
                save_steps=yaml_config.save_steps,
                eval_steps=yaml_config.eval_steps,
                logging_steps=yaml_config.logging_steps,
                save_total_limit=yaml_config.save_total_limit,
                evaluation_strategy=yaml_config.evaluation_strategy,
                load_best_model_at_end=yaml_config.load_best_model_at_end,
                metric_for_best_model=yaml_config.metric_for_best_model,
                dataloader_pin_memory=yaml_config.dataloader_pin_memory,
                dataloader_num_workers=yaml_config.dataloader_num_workers,
                remove_unused_columns=yaml_config.remove_unused_columns,
                bf16=yaml_config.bf16,
                optim=yaml_config.optim,
                report_to=yaml_config.report_to,
                run_name=yaml_config.run_name
            )
            logger.info(f"Loaded configuration from {args.config}")
        except ImportError:
            logger.warning("Could not import config_loader. Install PyYAML to use YAML configuration.")
            config = None
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            sys.exit(1)
    else:
        config = None

    # Create configuration (use YAML config if loaded, otherwise use command line args)
    if config is None:
        config = ContinuedPretrainingConfig(
            model_path=args.model_path,
            output_dir=args.output_dir,
            dataset_name=args.dataset_name,
            learning_rate=args.learning_rate,
            num_train_epochs=args.num_epochs,
            per_device_train_batch_size=args.batch_size,
            use_lora=args.use_lora,
            report_to=["wandb"] if args.use_wandb else ["tensorboard"]
        )

    # Override with command line arguments if provided
    if args.use_wandb:
        config.report_to = ["wandb"]
    if args.resume_from_checkpoint:
        config.resume_from_checkpoint = args.resume_from_checkpoint

    # Create output directory
    os.makedirs(config.output_dir, exist_ok=True)

    # Save configuration
    with open(os.path.join(config.output_dir, "training_config.json"), "w") as f:
        json.dump(config.__dict__, f, indent=2)

    logger.info("Starting PaliGemma Continued Pre-training for Image Captioning")
    logger.info(f"Configuration: {config}")

    # Initialize wandb if requested
    if args.use_wandb:
        wandb.init(
            project="paligemma-continued-pretraining",
            name=config.run_name,
            config=config.__dict__
        )

    try:
        # Setup model and processor
        model, processor = setup_model_and_processor(config)

        # Load and prepare datasets
        train_dataset, eval_dataset = load_and_prepare_dataset(config, processor)

        # Create training arguments
        training_args = create_training_arguments(config)

        # Create custom data collator
        def data_collator(batch):
            return collate_fn(batch, processor, model.device)

        # Initialize trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
            callbacks=[MemoryCallback()]
        )

        # Resume from checkpoint if specified
        if args.resume_from_checkpoint:
            logger.info(f"Resuming from checkpoint: {args.resume_from_checkpoint}")
            trainer.train(resume_from_checkpoint=args.resume_from_checkpoint)
        else:
            # Start training
            logger.info("Starting training...")
            trainer.train()

        # Save final model
        logger.info("Saving final model...")
        trainer.save_model()
        processor.save_pretrained(config.output_dir)

        # Save training metrics
        if trainer.state.log_history:
            with open(os.path.join(config.output_dir, "training_metrics.json"), "w") as f:
                json.dump(trainer.state.log_history, f, indent=2)

        logger.info("Training completed successfully!")

        # Final memory cleanup
        clear_gpu_memory()

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise
    finally:
        if args.use_wandb:
            wandb.finish()

def evaluate_model(model_path: str, dataset_name: str = "nlphuji/flickr30k", num_samples: int = 100):
    """
    Evaluate the continued pre-trained model on image captioning

    Args:
        model_path: Path to the trained model
        dataset_name: Dataset to evaluate on
        num_samples: Number of samples to evaluate
    """
    logger.info(f"Evaluating model from {model_path}")

    # Load model and processor
    processor = PaliGemmaProcessor.from_pretrained(model_path)
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto"
    ).eval()

    # Load evaluation dataset
    eval_dataset = load_dataset(dataset_name, split="test")
    eval_dataset = eval_dataset.select(range(min(num_samples, len(eval_dataset))))

    results = []

    with torch.no_grad():
        for i, item in enumerate(tqdm(eval_dataset, desc="Evaluating")):
            try:
                image = item['image'].convert('RGB')
                ground_truth = item['caption'][0] if isinstance(item['caption'], list) else item['caption']

                # Generate caption
                prompt = "Describe this image:"
                inputs = processor(text=prompt, images=image, return_tensors="pt").to(model.device)

                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=100,
                    do_sample=False,
                    temperature=0.0
                )

                input_len = inputs["input_ids"].shape[-1]
                generated_caption = processor.batch_decode(
                    generated_ids[:, input_len:],
                    skip_special_tokens=True
                )[0]

                results.append({
                    'sample_id': i,
                    'ground_truth': ground_truth,
                    'generated_caption': generated_caption
                })

                if i < 5:  # Print first 5 examples
                    logger.info(f"Sample {i}:")
                    logger.info(f"  Ground truth: {ground_truth}")
                    logger.info(f"  Generated: {generated_caption}")
                    logger.info("-" * 50)

            except Exception as e:
                logger.warning(f"Error evaluating sample {i}: {e}")
                continue

    # Save results
    output_file = os.path.join(model_path, "evaluation_results.json")
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)

    logger.info(f"Evaluation completed. Results saved to {output_file}")
    return results

if __name__ == "__main__":
    # Set up environment
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # Enable TF32 for better performance on Ampere GPUs
    if torch.cuda.is_available():
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True

    main()
