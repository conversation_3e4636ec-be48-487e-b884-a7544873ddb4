# PaliGemma Continued Pre-training for Image Captioning

This directory contains scripts for performing continued pre-training (also known as continual pre-training) of PaliGemma models specifically adapted for image captioning tasks.

## 🎯 What is Continued Pre-training?

Continued pre-training is a technique that bridges the gap between initial pre-training and task-specific fine-tuning. It involves:

- **Domain Adaptation**: Adapting a pre-trained model to a specific domain (image captioning)
- **Lower Learning Rates**: Using learning rates lower than initial pre-training but higher than fine-tuning
- **Longer Training**: Extended training schedules with careful warmup for stability
- **Broader Knowledge**: Focus on general domain knowledge rather than task-specific performance

## 📁 Files Overview

- `continued_pretraining_paligemma_captioning.py` - Main training script
- `run_continued_pretraining.py` - Easy-to-use runner script
- `README_continued_pretraining.md` - This documentation

## 🚀 Quick Start

### 1. Check System Setup
```bash
python run_continued_pretraining.py --check-setup
```

### 2. Quick Test Run
```bash
python run_continued_pretraining.py --quick-test
```

### 3. Full Training
```bash
python run_continued_pretraining.py --full-training
```

### 4. Evaluate Trained Model
```bash
python run_continued_pretraining.py --evaluate /path/to/trained/model
```

## 🔧 Advanced Usage

### Custom Training Configuration
```bash
python continued_pretraining_paligemma_captioning.py \
    --model_path "/path/to/paligemma/model" \
    --output_dir "./my_continued_pretraining" \
    --dataset_name "nlphuji/flickr30k" \
    --learning_rate 5e-6 \
    --num_epochs 3 \
    --batch_size 2 \
    --use_lora \
    --use_wandb
```

### Resume from Checkpoint
```bash
python continued_pretraining_paligemma_captioning.py \
    --resume_from_checkpoint "/path/to/checkpoint"
```

## ⚙️ Configuration Options

### Key Hyperparameters for Continued Pre-training

| Parameter | Default | Description |
|-----------|---------|-------------|
| `learning_rate` | 5e-6 | Lower than fine-tuning (2e-5) for stability |
| `warmup_ratio` | 0.1 | Longer warmup for gradual adaptation |
| `num_train_epochs` | 3 | Extended training for domain adaptation |
| `weight_decay` | 0.01 | Regularization for stability |

### LoRA Configuration
- `lora_r`: 16 (rank of adaptation)
- `lora_alpha`: 32 (scaling factor)
- `lora_dropout`: 0.1 (dropout for regularization)

### Memory Optimization
- Uses LoRA for parameter-efficient training
- Gradient accumulation for effective larger batch sizes
- Mixed precision training (bfloat16)
- Automatic memory management and cleanup

## 📊 Supported Datasets

The script supports various image captioning datasets:

- **Flickr30k** (`nlphuji/flickr30k`) - Default dataset
- **COCO Captions** - Can be configured
- **Custom datasets** - Must have `image` and `caption` fields

### Dataset Format Requirements
Your dataset should have:
- `image`: PIL Image or image array
- `caption`: String or list of strings

## 🎛️ Training Process

### Phase 1: Model Setup
1. Load pre-trained PaliGemma model
2. Freeze vision encoder for stability
3. Keep multi-modal projector trainable
4. Apply LoRA to language model components

### Phase 2: Data Preparation
1. Load and preprocess captioning dataset
2. Create train/validation splits
3. Format prompts for continued pre-training
4. Apply data collation and batching

### Phase 3: Training
1. Lower learning rate schedule with warmup
2. Extended training with stability monitoring
3. Regular checkpointing and evaluation
4. Memory management and cleanup

## 📈 Monitoring and Logging

### TensorBoard (Default)
```bash
tensorboard --logdir ./paligemma_continued_pretraining_captioning/runs
```

### Weights & Biases
Add `--use_wandb` flag to enable W&B logging.

### Memory Monitoring
The script includes automatic GPU memory monitoring and cleanup.

## 🔍 Evaluation

The evaluation script generates captions for test images and compares them with ground truth:

```python
from continued_pretraining_paligemma_captioning import evaluate_model

results = evaluate_model(
    model_path="/path/to/trained/model",
    dataset_name="nlphuji/flickr30k",
    num_samples=100
)
```

## 🛠️ Troubleshooting

### Common Issues

1. **GPU Out of Memory**
   - Reduce batch size: `--batch_size 1`
   - Increase gradient accumulation: modify `gradient_accumulation_steps`
   - Use LoRA: `--use_lora` (enabled by default)

2. **Slow Training**
   - Ensure GPU is available and CUDA is properly installed
   - Enable mixed precision (enabled by default)
   - Use appropriate number of dataloader workers

3. **Dataset Loading Issues**
   - Check dataset name and format
   - Ensure internet connection for downloading
   - Verify dataset has required fields (`image`, `caption`)

### Memory Management
The script includes several memory optimization techniques:
- Automatic GPU cache clearing
- Garbage collection at epoch boundaries
- Memory usage monitoring
- Efficient data loading

## 📋 Requirements

### Python Packages
```
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
peft>=0.6.0
pillow>=9.0.0
numpy>=1.21.0
tqdm>=4.64.0
wandb>=0.15.0 (optional)
```

### Hardware Requirements
- **Minimum**: 16GB GPU memory (with LoRA)
- **Recommended**: 24GB+ GPU memory
- **CPU**: Multi-core processor for data loading
- **Storage**: 50GB+ for datasets and checkpoints

## 🔬 Research Background

This implementation is based on recent research in continued pre-training:

1. **Domain-Adaptive Pre-training**: Adapting models to specific domains
2. **Learning Rate Schedules**: Optimal schedules for continued training
3. **Parameter-Efficient Methods**: LoRA for memory-efficient adaptation
4. **Vision-Language Models**: Best practices for multimodal training

## 📝 Citation

If you use this code in your research, please cite:

```bibtex
@misc{paligemma_continued_pretraining,
  title={PaliGemma Continued Pre-training for Image Captioning},
  author={Johnny},
  year={2025},
  url={https://github.com/your-repo/smol-vision}
}
```

## 🤝 Contributing

Feel free to submit issues and pull requests to improve the code!

## 📄 License

This project follows the same license as the parent smol-vision repository.
