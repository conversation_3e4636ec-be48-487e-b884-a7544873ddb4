# Configuration file for PaliGemma Continued Pre-training
# This file contains all the hyperparameters and settings for continued pre-training

# Model and Data Configuration
model:
  model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224"
  output_dir: "./paligemma_continued_pretraining_captioning"
  dataset_name: "nlphuji/flickr30k"  # Options: nlphuji/flickr30k, custom dataset

# Training Hyperparameters (Optimized for Continued Pre-training)
training:
  num_train_epochs: 3
  per_device_train_batch_size: 2
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 8
  learning_rate: 5e-6  # Lower than fine-tuning for stability
  weight_decay: 0.01
  warmup_ratio: 0.1    # Longer warmup for gradual adaptation
  max_grad_norm: 1.0

# LoRA Configuration for Memory Efficiency
lora:
  use_lora: true
  lora_r: 16
  lora_alpha: 32
  lora_dropout: 0.1
  target_modules:
    - "q_proj"
    - "k_proj" 
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Training Monitoring and Checkpointing
monitoring:
  save_steps: 500
  eval_steps: 500
  logging_steps: 50
  save_total_limit: 3
  evaluation_strategy: "steps"
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"

# Memory and Performance Optimization
optimization:
  dataloader_pin_memory: false
  dataloader_num_workers: 4
  remove_unused_columns: false
  bf16: true  # Mixed precision training
  tf32: true  # TensorFloat-32 for Ampere GPUs
  optim: "adamw_torch_fused"  # Optimized AdamW

# Logging and Monitoring
logging:
  report_to: ["tensorboard"]  # Options: tensorboard, wandb
  run_name: "paligemma_continued_pretraining"
  project_name: "paligemma-continued-pretraining"  # For wandb

# Dataset Configuration
dataset:
  max_length: 512
  train_split_ratio: 0.9
  validation_split_ratio: 0.1
  image_size: 224
  
# Evaluation Configuration
evaluation:
  num_eval_samples: 100
  generation_max_new_tokens: 100
  generation_do_sample: false
  generation_temperature: 0.0

# Advanced Training Settings
advanced:
  dataloader_drop_last: true
  ignore_data_skip: true
  resume_from_checkpoint: null
  push_to_hub: false
  hub_model_id: null

# System Configuration
system:
  cuda_visible_devices: "0"  # GPU device(s) to use
  tokenizers_parallelism: false
  
# Continued Pre-training Specific Settings
continued_pretraining:
  # Components to train (true) or freeze (false)
  train_vision_tower: false      # Keep frozen for stability
  train_multimodal_projector: true  # Train for domain adaptation
  train_language_model: true     # Train with LoRA
  
  # Learning rate schedule
  lr_scheduler_type: "cosine"    # Options: linear, cosine, polynomial
  
  # Domain adaptation settings
  domain_adaptation_warmup: true
  domain_specific_prompts: true
  
  # Caption formatting
  caption_prompt_template: "Describe this image:"
  caption_max_length: 100

# Data Augmentation (Optional)
data_augmentation:
  enabled: false
  techniques:
    - "random_resize_crop"
    - "color_jitter"
    - "horizontal_flip"

# Experimental Features
experimental:
  use_gradient_checkpointing: false
  use_deepspeed: false
  use_fsdp: false
