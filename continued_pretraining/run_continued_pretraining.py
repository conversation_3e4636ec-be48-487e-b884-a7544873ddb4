#!/usr/bin/env python3
"""
Easy-to-use runner script for PaliGemma continued pre-training

This script provides a simple interface to run continued pre-training with
sensible defaults and automatic configuration.

Usage:
    python run_continued_pretraining.py --quick-test  # Quick test run
    python run_continued_pretraining.py --full-training  # Full training
    python run_continued_pretraining.py --evaluate /path/to/model  # Evaluate model

Author: Johnny
Date: 2025-07-15
"""

import argparse
import os
import sys
import subprocess

def run_quick_test():
    """Run a quick test with minimal resources"""
    print("🚀 Running quick test for continued pre-training...")
    
    cmd = [
        sys.executable, "continued_pretraining_paligemma_captioning.py",
        "--num_epochs", "1",
        "--batch_size", "1",
        "--learning_rate", "1e-5",
        "--output_dir", "./test_continued_pretraining",
        "--use_lora"
    ]
    
    subprocess.run(cmd, check=True)
    print("✅ Quick test completed!")

def run_full_training():
    """Run full continued pre-training"""
    print("🚀 Starting full continued pre-training...")
    
    cmd = [
        sys.executable, "continued_pretraining_paligemma_captioning.py",
        "--num_epochs", "3",
        "--batch_size", "2",
        "--learning_rate", "5e-6",
        "--output_dir", "./paligemma_continued_pretraining_captioning",
        "--use_lora",
        "--use_wandb"
    ]
    
    subprocess.run(cmd, check=True)
    print("✅ Full training completed!")

def run_evaluation(model_path: str):
    """Run evaluation on trained model"""
    print(f"🔍 Evaluating model at {model_path}...")
    
    # Import and run evaluation
    sys.path.append(os.path.dirname(__file__))
    from continued_pretraining_paligemma_captioning import evaluate_model
    
    results = evaluate_model(model_path)
    print(f"✅ Evaluation completed! Results saved in {model_path}/evaluation_results.json")
    return results

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        "torch",
        "transformers",
        "datasets",
        "peft",
        "PIL",  # pillow imports as PIL
        "numpy",
        "tqdm"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed!")
    return True

def check_gpu():
    """Check GPU availability and memory"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ Found {gpu_count} GPU(s)")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            return True
        else:
            print("⚠️  No GPU found. Training will be very slow on CPU.")
            return False
    except ImportError:
        print("❌ PyTorch not installed")
        return False

def check_model_path(model_path: str):
    """Check if model path exists"""
    if os.path.exists(model_path):
        print(f"✅ Model found at {model_path}")
        return True
    else:
        print(f"❌ Model not found at {model_path}")
        print("Please download the model or update the path in the script")
        return False

def main():
    parser = argparse.ArgumentParser(description="Easy runner for PaliGemma continued pre-training")
    parser.add_argument("--quick-test", action="store_true", 
                       help="Run a quick test with minimal resources")
    parser.add_argument("--full-training", action="store_true",
                       help="Run full continued pre-training")
    parser.add_argument("--evaluate", type=str, metavar="MODEL_PATH",
                       help="Evaluate a trained model")
    parser.add_argument("--check-setup", action="store_true",
                       help="Check system setup and requirements")
    parser.add_argument("--model-path", type=str, 
                       default="/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224",
                       help="Path to base PaliGemma model")
    
    args = parser.parse_args()
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    if args.check_setup:
        print("🔍 Checking system setup...")
        all_good = True
        all_good &= check_requirements()
        all_good &= check_gpu()
        all_good &= check_model_path(args.model_path)
        
        if all_good:
            print("\n✅ System setup looks good! You can proceed with training.")
        else:
            print("\n❌ Please fix the issues above before proceeding.")
            sys.exit(1)
    
    elif args.quick_test:
        if not check_requirements():
            sys.exit(1)
        run_quick_test()
    
    elif args.full_training:
        if not check_requirements():
            sys.exit(1)
        run_full_training()
    
    elif args.evaluate:
        if not check_requirements():
            sys.exit(1)
        run_evaluation(args.evaluate)
    
    else:
        print("Please specify an action:")
        print("  --check-setup     Check system requirements")
        print("  --quick-test      Run quick test")
        print("  --full-training   Run full training")
        print("  --evaluate PATH   Evaluate trained model")
        print("\nFor more options, see: python continued_pretraining_paligemma_captioning.py --help")

if __name__ == "__main__":
    main()
