#!/usr/bin/env python3
"""
Simple run script for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import os
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from main_evaluation import main

if __name__ == "__main__":
    # Set environment variables if needed
    os.environ.setdefault('TOKENIZERS_PARALLELISM', 'false')  # Avoid tokenizer warnings
    
    # Run main evaluation
    main()
