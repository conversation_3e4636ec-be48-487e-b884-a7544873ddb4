#!/usr/bin/env python3
"""
Example usage of PaliGemma2 evaluation benchmark
Author: Johnny
"""

import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from main_evaluation import PaliGemmaEvaluationBenchmark
from utils.config import get_default_config_path

def run_basic_evaluation():
    """Run basic evaluation with default configuration"""
    print("Running basic evaluation...")
    
    # Use default configuration
    config_path = get_default_config_path()
    
    # Initialize benchmark
    benchmark = PaliGemmaEvaluationBenchmark(config_path)
    
    # Run evaluation
    results = benchmark.run_evaluation()
    
    # Print summary
    print("\n" + "="*60)
    print("EVALUATION SUMMARY")
    print("="*60)
    
    # Binary results summary
    if 'binary_results' in results:
        print("\nBinary Classification Results:")
        for model_name, model_results in results['binary_results'].items():
            accuracy = model_results.get('binary_accuracy', 0)
            f1 = model_results.get('binary_f1', 0)
            print(f"  {model_name}: Accuracy={accuracy:.3f}, F1={f1:.3f}")
    
    # Text results summary
    if 'text_results' in results:
        print("\nText Generation Results:")
        for model_name, model_results in results['text_results'].items():
            bleu = model_results.get('bleu_4', 0)
            rouge = model_results.get('rougeL_f1', 0)
            semantic = model_results.get('semantic_similarity_mean', 0)
            print(f"  {model_name}: BLEU={bleu:.3f}, ROUGE-L={rouge:.3f}, Semantic={semantic:.3f}")
    
    # Best model
    if 'comparative_results' in results and 'overall_ranking' in results['comparative_results']:
        ranking = results['comparative_results']['overall_ranking']
        best_model = ranking.get('best_model')
        if best_model:
            print(f"\nBest performing model: {best_model}")
    
    print(f"\nResults saved to run directory: {benchmark.run_dir}")
    print(f"  - Reports: {benchmark.reports_dir}")
    print(f"  - Visualizations: {benchmark.visualizations_dir}")

    return results

def run_custom_evaluation():
    """Run evaluation with custom settings"""
    print("Running custom evaluation...")
    
    # Load default config
    config_path = get_default_config_path()
    benchmark = PaliGemmaEvaluationBenchmark(config_path)
    
    # Load samples
    samples = benchmark.data_loader.load_samples()
    
    # Filter to only binary samples for quick test
    binary_samples = benchmark.data_loader.filter_samples(
        samples, 
        response_type='binary',
        max_samples=10  # Limit for quick test
    )
    
    print(f"Testing with {len(binary_samples)} binary samples")
    
    # Run inference
    model_results = benchmark.model_inference.run_inference(binary_samples)
    
    # Run only binary evaluation
    binary_results = benchmark.binary_evaluator.evaluate(binary_samples, model_results)
    
    # Print results
    print("\nBinary Evaluation Results:")
    for model_name, results in binary_results.items():
        print(f"\n{model_name}:")
        for metric, value in results.items():
            if isinstance(value, (int, float)):
                print(f"  {metric}: {value:.4f}")
    
    return binary_results

def demonstrate_individual_components():
    """Demonstrate using individual components"""
    print("Demonstrating individual components...")
    
    # Load configuration
    config_path = get_default_config_path()
    benchmark = PaliGemmaEvaluationBenchmark(config_path)
    
    # 1. Data loading
    print("\n1. Loading data...")
    samples = benchmark.data_loader.load_samples()
    stats = benchmark.data_loader.get_statistics(samples)
    print(f"   Loaded {stats['total_samples']} samples")
    print(f"   Binary: {stats['binary_samples']}, Detailed: {stats['detailed_samples']}")
    
    # 2. Model inference (on subset)
    print("\n2. Running inference on 5 samples...")
    test_samples = samples[:5]
    
    # Load images
    for sample in test_samples:
        sample.image = benchmark.data_loader.load_image(sample.image_path)
    
    model_results = benchmark.model_inference.run_inference(test_samples)
    
    # 3. Show predictions
    print("\n3. Sample predictions:")
    for sample in test_samples:
        print(f"\nImage: {sample.image_name}")
        print(f"Question: {sample.question}")
        print(f"Ground truth: {sample.ground_truth}")
        
        for model_name, results in model_results.items():
            prediction = results.get(sample.sample_id, "")
            print(f"{model_name} prediction: {prediction}")

def main():
    """Main function with different usage examples"""
    print("PaliGemma2 Evaluation Benchmark - Usage Examples")
    print("=" * 60)
    
    # Check if configuration exists
    config_path = get_default_config_path()
    if not Path(config_path).exists():
        print(f"Error: Configuration file not found at {config_path}")
        print("Please run setup.py first or create the configuration file.")
        return
    
    try:
        # Example 1: Basic evaluation
        print("\nExample 1: Basic Evaluation")
        print("-" * 30)
        run_basic_evaluation()
        
        # Example 2: Custom evaluation
        print("\n\nExample 2: Custom Evaluation")
        print("-" * 30)
        run_custom_evaluation()
        
        # Example 3: Individual components
        print("\n\nExample 3: Individual Components")
        print("-" * 30)
        demonstrate_individual_components()
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        print("Please check your configuration and model paths.")

if __name__ == "__main__":
    main()
