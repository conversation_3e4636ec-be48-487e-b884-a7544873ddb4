"""
Main evaluation script for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import argparse
import json
import csv
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add evaluation_benchmark directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))
# Add parent directory to path for package imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from evaluation_benchmark.utils.config import ConfigLoader, get_default_config_path
from evaluation_benchmark.utils.logging_utils import setup_logging, ProgressTracker, MultiProgressTracker
from evaluation_benchmark.utils.visualization import EvaluationVisualizer
from evaluation_benchmark.core.data_loader import create_data_loader
from evaluation_benchmark.core.model_inference import MultiModelInference
from evaluation_benchmark.evaluators.binary_evaluator import BinaryEvaluator
from evaluation_benchmark.evaluators.text_evaluator import TextEvaluator
from evaluation_benchmark.evaluators.comparative_evaluator import ComparativeEvaluator


class PaliGemmaEvaluationBenchmark:
    """Main evaluation benchmark class"""
    
    def __init__(self, config_path: str):
        """
        Initialize evaluation benchmark

        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        self.config = ConfigLoader.load_config(config_path)
        ConfigLoader.validate_config(self.config)

        # Create run-specific directory
        self.run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = Path(self.config.output.results_dir) / f"run_{self.run_timestamp}"
        self.run_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories for organized storage
        self.reports_dir = self.run_dir / "reports"
        self.visualizations_dir = self.run_dir / "visualizations"
        self.reports_dir.mkdir(exist_ok=True)
        self.visualizations_dir.mkdir(exist_ok=True)

        # Update config to use run-specific directory
        self.config.output.results_dir = str(self.run_dir)

        # Setup logging with run-specific log file
        self.logger = setup_logging(self.config)

        # Initialize components
        self.data_loader = create_data_loader(self.config)
        self.model_inference = MultiModelInference(
            self.config.models,
            self.config.inference,
            self.logger
        )

        # Initialize evaluators
        self.binary_evaluator = BinaryEvaluator(
            self.config.metrics.binary_metrics.get('binary_keywords'),
            self.logger
        )

        self.text_evaluator = TextEvaluator(
            self.config.metrics.semantic_metrics,
            self.logger
        )

        self.comparative_evaluator = ComparativeEvaluator(
            self.config.statistics.confidence_level,
            self.config.statistics.bootstrap_samples,
            self.logger
        )

        # Initialize visualizer with run-specific visualizations directory
        self.visualizer = EvaluationVisualizer(str(self.visualizations_dir))

        # Results storage
        self.results = {}
    
    def run_evaluation(self) -> Dict[str, Any]:
        """
        Run complete evaluation benchmark
        
        Returns:
            Dictionary with all evaluation results
        """
        self.logger.info("Starting PaliGemma2 Evaluation Benchmark")
        
        # Load data
        samples = self._load_data()
        
        # Run model inference
        model_results = self._run_inference(samples)
        
        # Run evaluations
        evaluation_results = self._run_evaluations(samples, model_results)
        
        # Generate reports
        self._generate_reports(evaluation_results)
        
        # Create visualizations
        if self.config.output.generate_visualizations:
            self._create_visualizations(evaluation_results)

        # Log completion with run directory info
        self.logger.info(f"Evaluation completed successfully!")
        self.logger.info(f"All results saved to: {self.run_dir}")
        self.logger.info(f"Reports directory: {self.reports_dir}")
        self.logger.info(f"Visualizations directory: {self.visualizations_dir}")

        return evaluation_results
    
    def _load_data(self) -> List:
        """Load evaluation data"""
        self.logger.info("Loading evaluation data...")

        # Load samples with progress bar
        samples = self.data_loader.load_samples(
            show_progress=self.config.logging.progress_bar
        )

        # Log dataset statistics
        stats = self.data_loader.get_statistics(samples)
        self.logger.info(f"Loaded {stats['total_samples']} samples:")
        self.logger.info(f"  - Binary samples: {stats['binary_samples']} ({stats['binary_ratio']:.1%})")
        self.logger.info(f"  - Detailed samples: {stats['detailed_samples']} ({stats['detailed_ratio']:.1%})")
        self.logger.info(f"  - Unique images: {stats['unique_images']}")

        self.logger.start_evaluation(len(samples))

        return samples
    
    def _run_inference(self, samples: List) -> Dict[str, Dict[str, str]]:
        """Run model inference on all samples"""
        self.logger.info("Running model inference...")
        
        # Load images for samples
        failed_images = 0
        with ProgressTracker(
            len(samples),
            "Loading images",
            disable=not self.config.logging.progress_bar,
            unit="img",
            colour="blue"
        ) as pbar:
            for idx, sample in enumerate(samples):
                try:
                    sample.image = self.data_loader.load_image(sample.image_path)
                    pbar.set_postfix(
                        current=Path(sample.image_path).name,
                        failed=failed_images,
                        success_rate=f"{((idx + 1 - failed_images) / (idx + 1) * 100):.1f}%"
                    )
                except Exception as e:
                    failed_images += 1
                    self.logger.error(f"Failed to load image {sample.image_path}: {e}")
                    pbar.set_postfix(
                        current=Path(sample.image_path).name,
                        failed=failed_images,
                        success_rate=f"{((idx + 1 - failed_images) / (idx + 1) * 100):.1f}%"
                    )
                finally:
                    pbar.update()
        
        # Run inference
        model_results = self.model_inference.run_inference(
            samples,
            show_progress=self.config.logging.progress_bar
        )
        
        # Log inference results
        for model_name, results in model_results.items():
            successful_inferences = sum(1 for response in results.values() if response.strip())
            self.logger.info(f"{model_name}: {successful_inferences}/{len(results)} successful inferences")
        
        return model_results
    
    def _run_evaluations(self, samples: List, model_results: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """Run all evaluations"""
        evaluation_results = {
            'metadata': {
                'evaluation_date': datetime.now().isoformat(),
                'total_samples': len(samples),
                'models_evaluated': list(model_results.keys()),
                'config_used': self.config.__dict__
            }
        }
        
        # Binary evaluation
        if self.config.metrics.binary_metrics.get('enabled', True):
            self.logger.info("Running binary classification evaluation...")
            binary_results = self.binary_evaluator.evaluate(
                samples,
                model_results,
                show_progress=self.config.logging.progress_bar
            )
            evaluation_results['binary_results'] = binary_results
        else:
            evaluation_results['binary_results'] = {}

        # Text evaluation
        if self.config.metrics.text_metrics.get('enabled', True):
            self.logger.info("Running text generation evaluation...")
            text_results = self.text_evaluator.evaluate(
                samples,
                model_results,
                show_progress=self.config.logging.progress_bar
            )
            evaluation_results['text_results'] = text_results
        else:
            evaluation_results['text_results'] = {}
        
        # Comparative evaluation
        self.logger.info("Running comparative evaluation...")
        comparative_results = self.comparative_evaluator.compare_models(
            samples, model_results, 
            evaluation_results['binary_results'],
            evaluation_results['text_results']
        )
        evaluation_results['comparative_results'] = comparative_results
        
        # Individual sample results (if enabled)
        if self.config.output.save_individual_results:
            individual_results = self._compile_individual_results(
                samples, model_results, evaluation_results
            )
            evaluation_results['individual_results'] = individual_results
        
        return evaluation_results
    
    def _compile_individual_results(self, 
                                   samples: List, 
                                   model_results: Dict[str, Dict[str, str]],
                                   evaluation_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Compile individual sample results"""
        individual_results = []
        
        for sample in samples:
            sample_result = {
                'sample_id': sample.sample_id,
                'image_name': sample.image_name,
                'question': sample.question,
                'ground_truth': sample.ground_truth,
                'response_type': sample.response_type,
                'model_predictions': {}
            }
            
            # Add model predictions
            for model_name, results in model_results.items():
                if sample.sample_id in results:
                    sample_result['model_predictions'][model_name] = results[sample.sample_id]
            
            individual_results.append(sample_result)
        
        return individual_results
    
    def _generate_reports(self, evaluation_results: Dict[str, Any]):
        """Generate evaluation reports"""
        self.logger.info("Generating evaluation reports...")

        # JSON report
        if 'json' in self.config.output.export_formats:
            json_path = self.reports_dir / "evaluation_results.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(evaluation_results, f, indent=2, ensure_ascii=False, default=str)
            self.logger.info(f"JSON report saved: {json_path}")

        # CSV summary report
        if 'csv' in self.config.output.export_formats:
            csv_path = self.reports_dir / "evaluation_summary.csv"
            self._create_csv_summary(evaluation_results, csv_path)
            self.logger.info(f"CSV report saved: {csv_path}")

        # HTML report
        if 'html' in self.config.output.export_formats:
            html_path = self.reports_dir / "evaluation_report.html"
            self._create_html_report(evaluation_results, html_path)
            self.logger.info(f"HTML report saved: {html_path}")

        # Save run metadata
        metadata_path = self.run_dir / "run_metadata.json"
        run_metadata = {
            'run_id': f"run_{self.run_timestamp}",
            'timestamp': self.run_timestamp,
            'start_time': evaluation_results.get('metadata', {}).get('evaluation_date'),
            'config_used': evaluation_results.get('metadata', {}).get('config_used'),
            'models_evaluated': evaluation_results.get('metadata', {}).get('models_evaluated'),
            'total_samples': evaluation_results.get('metadata', {}).get('total_samples'),
            'reports_generated': {
                'json': 'json' in self.config.output.export_formats,
                'csv': 'csv' in self.config.output.export_formats,
                'html': 'html' in self.config.output.export_formats
            },
            'visualizations_generated': self.config.output.generate_visualizations
        }
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(run_metadata, f, indent=2, ensure_ascii=False, default=str)
        self.logger.info(f"Run metadata saved: {metadata_path}")
    
    def _create_csv_summary(self, evaluation_results: Dict[str, Any], csv_path: Path):
        """Create CSV summary report"""
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Header
            writer.writerow(['Model', 'Metric', 'Value'])
            
            # Binary results
            for model_name, results in evaluation_results.get('binary_results', {}).items():
                for metric, value in results.items():
                    if isinstance(value, (int, float)):
                        writer.writerow([model_name, f"binary_{metric}", value])
            
            # Text results
            for model_name, results in evaluation_results.get('text_results', {}).items():
                for metric, value in results.items():
                    if isinstance(value, (int, float)):
                        writer.writerow([model_name, f"text_{metric}", value])
    
    def _create_html_report(self, evaluation_results: Dict[str, Any], html_path: Path):
        """Create HTML report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>PaliGemma2 Evaluation Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metric-section {{ margin: 30px 0; }}
                .model-comparison {{ background-color: #f9f9f9; padding: 15px; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <h1>PaliGemma2 Evaluation Report</h1>
            <p>Generated on: {evaluation_results['metadata']['evaluation_date']}</p>
            <p>Total samples: {evaluation_results['metadata']['total_samples']}</p>
            <p>Models evaluated: {', '.join(evaluation_results['metadata']['models_evaluated'])}</p>
            
            <div class="metric-section">
                <h2>Binary Classification Results</h2>
                {self._format_results_table(evaluation_results.get('binary_results', {}))}
            </div>
            
            <div class="metric-section">
                <h2>Text Generation Results</h2>
                {self._format_results_table(evaluation_results.get('text_results', {}))}
            </div>
            
            <div class="metric-section">
                <h2>Model Comparison</h2>
                {self._format_comparison_results(evaluation_results.get('comparative_results', {}))}
            </div>
        </body>
        </html>
        """
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _format_results_table(self, results: Dict[str, Any]) -> str:
        """Format results as HTML table"""
        if not results:
            return "<p>No results available</p>"
        
        html = "<table><tr><th>Model</th><th>Metric</th><th>Value</th></tr>"
        
        for model_name, model_results in results.items():
            for metric, value in model_results.items():
                if isinstance(value, (int, float)):
                    html += f"<tr><td>{model_name}</td><td>{metric}</td><td>{value:.4f}</td></tr>"
        
        html += "</table>"
        return html
    
    def _format_comparison_results(self, comparison_results: Dict[str, Any]) -> str:
        """Format comparison results as HTML"""
        if not comparison_results:
            return "<p>No comparison results available</p>"
        
        html = ""
        
        # Overall ranking
        if 'overall_ranking' in comparison_results:
            ranking = comparison_results['overall_ranking']
            html += f"<div class='model-comparison'><h3>Overall Ranking</h3>"
            html += f"<p>Best model: <strong>{ranking.get('best_model', 'N/A')}</strong></p>"
            html += "</div>"
        
        return html
    
    def _create_visualizations(self, evaluation_results: Dict[str, Any]):
        """Create visualizations"""
        self.logger.info("Creating visualizations...")
        
        try:
            created_files = self.visualizer.create_all_visualizations(
                evaluation_results.get('binary_results', {}),
                evaluation_results.get('text_results', {})
            )
            
            for file_path in created_files:
                self.logger.info(f"Created visualization: {file_path}")
                
        except Exception as e:
            self.logger.error(f"Failed to create visualizations: {e}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='PaliGemma2 Evaluation Benchmark')
    parser.add_argument('--config', type=str, default=None,
                       help='Path to configuration file')
    parser.add_argument('--output-dir', type=str, default=None,
                       help='Output directory (overrides config)')
    
    args = parser.parse_args()
    
    # Use default config if not provided
    config_path = args.config or get_default_config_path()
    
    try:
        # Initialize and run benchmark
        benchmark = PaliGemmaEvaluationBenchmark(config_path)
        
        # Override output directory if provided
        if args.output_dir:
            benchmark.config.output.results_dir = args.output_dir
        
        # Run evaluation
        results = benchmark.run_evaluation()
        
        print("\n" + "="*60)
        print("EVALUATION COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        # Print summary
        if 'comparative_results' in results and 'overall_ranking' in results['comparative_results']:
            ranking = results['comparative_results']['overall_ranking']
            print(f"Best performing model: {ranking.get('best_model', 'N/A')}")
        
        print(f"Results saved to: {benchmark.config.output.results_dir}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
