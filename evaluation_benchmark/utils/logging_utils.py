"""
Logging utilities for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from tqdm import tqdm
import time
from datetime import datetime


class EvaluationLogger:
    """Custom logger for evaluation benchmark"""
    
    def __init__(self, 
                 log_level: str = "INFO",
                 log_file: Optional[str] = None,
                 console_output: bool = True,
                 results_dir: Optional[str] = None):
        """
        Initialize logger
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
            log_file: Log file name (optional)
            console_output: Whether to output to console
            results_dir: Results directory for log file
        """
        self.logger = logging.getLogger("PaliGemmaEvaluation")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # File handler
        if log_file and results_dir:
            log_path = Path(results_dir) / log_file
            file_handler = logging.FileHandler(log_path)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        self.start_time = None
        self.current_step = 0
        self.total_steps = 0
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def start_evaluation(self, total_samples: int):
        """Start evaluation logging"""
        self.start_time = time.time()
        self.total_steps = total_samples
        self.current_step = 0
        
        self.info("="*60)
        self.info("Starting PaliGemma2 Evaluation Benchmark")
        self.info(f"Total samples to evaluate: {total_samples}")
        self.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info("="*60)
    
    def log_model_loading(self, model_name: str, model_path: str):
        """Log model loading"""
        self.info(f"Loading {model_name} model from: {model_path}")
    
    def log_model_loaded(self, model_name: str, load_time: float):
        """Log successful model loading"""
        self.info(f"✓ {model_name} model loaded successfully in {load_time:.2f}s")
    
    def log_inference_start(self, model_name: str, sample_count: int):
        """Log inference start"""
        self.info(f"Starting inference with {model_name} on {sample_count} samples")
    
    def log_inference_complete(self, model_name: str, inference_time: float):
        """Log inference completion"""
        self.info(f"✓ {model_name} inference completed in {inference_time:.2f}s")
    
    def log_metrics_calculation(self, metric_type: str):
        """Log metrics calculation"""
        self.info(f"Calculating {metric_type} metrics...")
    
    def log_metrics_complete(self, metric_type: str, calculation_time: float):
        """Log metrics calculation completion"""
        self.info(f"✓ {metric_type} metrics calculated in {calculation_time:.2f}s")
    
    def log_sample_processed(self, sample_id: str, model_name: str):
        """Log individual sample processing"""
        self.current_step += 1
        self.debug(f"Processed sample {sample_id} with {model_name} ({self.current_step}/{self.total_steps})")
    
    def log_error_sample(self, sample_id: str, model_name: str, error: str):
        """Log sample processing error"""
        self.error(f"Error processing sample {sample_id} with {model_name}: {error}")
    
    def log_results_saved(self, file_path: str):
        """Log results saving"""
        self.info(f"Results saved to: {file_path}")
    
    def finish_evaluation(self):
        """Finish evaluation logging"""
        if self.start_time:
            total_time = time.time() - self.start_time
            self.info("="*60)
            self.info("Evaluation completed successfully!")
            self.info(f"Total time: {total_time:.2f}s ({total_time/60:.2f} minutes)")
            self.info(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.info("="*60)


class ProgressTracker:
    """Enhanced progress tracking with tqdm"""

    def __init__(self,
                 total: int,
                 description: str = "Processing",
                 disable: bool = False,
                 unit: str = "sample",
                 unit_scale: bool = False,
                 leave: bool = True,
                 position: int = None,
                 colour: str = None):
        """
        Initialize progress tracker

        Args:
            total: Total number of items
            description: Progress bar description
            disable: Whether to disable progress bar
            unit: Unit of measurement for progress
            unit_scale: Whether to scale unit automatically
            leave: Whether to leave progress bar after completion
            position: Position of progress bar (for multiple bars)
            colour: Color of progress bar
        """
        self.pbar = tqdm(
            total=total,
            desc=description,
            disable=disable,
            unit=unit,
            unit_scale=unit_scale,
            leave=leave,
            position=position,
            colour=colour,
            ncols=120,  # Increased width for more info
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]'
        )
        self.start_time = time.time()
        self.last_update_time = self.start_time

    def update(self, n: int = 1):
        """Update progress with enhanced timing info"""
        current_time = time.time()
        self.pbar.update(n)
        self.last_update_time = current_time

    def set_description(self, desc: str):
        """Set progress description"""
        self.pbar.set_description(desc)

    def set_postfix(self, **kwargs):
        """Set progress postfix with enhanced info"""
        # Add timing information if not provided
        if 'elapsed' not in kwargs:
            elapsed = time.time() - self.start_time
            kwargs['elapsed'] = f"{elapsed:.1f}s"

        self.pbar.set_postfix(**kwargs)

    def set_postfix_str(self, s: str):
        """Set progress postfix as string"""
        self.pbar.set_postfix_str(s)

    def refresh(self):
        """Refresh the progress bar display"""
        self.pbar.refresh()

    def close(self):
        """Close progress bar"""
        self.pbar.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class MultiProgressTracker:
    """Manager for multiple progress bars"""

    def __init__(self, disable: bool = False):
        """
        Initialize multi-progress tracker

        Args:
            disable: Whether to disable all progress bars
        """
        self.disable = disable
        self.trackers = {}
        self.position_counter = 0

    def create_tracker(self,
                      name: str,
                      total: int,
                      description: str = "Processing",
                      unit: str = "sample",
                      colour: str = None) -> ProgressTracker:
        """
        Create a new progress tracker

        Args:
            name: Unique name for the tracker
            total: Total number of items
            description: Progress bar description
            unit: Unit of measurement
            colour: Color of progress bar

        Returns:
            ProgressTracker instance
        """
        tracker = ProgressTracker(
            total=total,
            description=description,
            disable=self.disable,
            unit=unit,
            position=self.position_counter,
            colour=colour,
            leave=True
        )
        self.trackers[name] = tracker
        self.position_counter += 1
        return tracker

    def get_tracker(self, name: str) -> ProgressTracker:
        """Get existing tracker by name"""
        return self.trackers.get(name)

    def close_all(self):
        """Close all progress trackers"""
        for tracker in self.trackers.values():
            tracker.close()
        self.trackers.clear()
        self.position_counter = 0

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_all()


def setup_logging(config) -> EvaluationLogger:
    """Setup logging based on configuration"""
    return EvaluationLogger(
        log_level=config.logging.level,
        log_file=config.logging.log_file if hasattr(config.logging, 'log_file') else None,
        console_output=config.logging.console_output,
        results_dir=config.output.results_dir
    )
