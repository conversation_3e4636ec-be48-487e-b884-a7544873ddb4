"""
Visualization utilities for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
import os
from pathlib import Path

# Set style
plt.style.use('default')
sns.set_palette("husl")


class EvaluationVisualizer:
    """Visualizer for evaluation results"""
    
    def __init__(self, output_dir: str):
        """
        Initialize visualizer

        Args:
            output_dir: Directory to save visualizations (should be the visualizations directory)
        """
        self.viz_dir = Path(output_dir)
        self.viz_dir.mkdir(parents=True, exist_ok=True)
    
    def create_performance_radar_chart(self, 
                                     binary_results: Dict[str, Any],
                                     text_results: Dict[str, Any],
                                     save_name: str = "performance_radar.png") -> str:
        """
        Create radar chart comparing model performance
        
        Args:
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            save_name: Name for saved file
            
        Returns:
            Path to saved visualization
        """
        # Define metrics for radar chart
        metrics = {
            'Binary Accuracy': ('binary_accuracy', binary_results),
            'Binary F1': ('binary_f1', binary_results),
            'BLEU-4': ('bleu_4', text_results),
            'ROUGE-L': ('rougeL_f1', text_results),
            'METEOR': ('meteor', text_results),
            'BERTScore': ('bertscore_f1', text_results),
            'Semantic Sim.': ('semantic_similarity_mean', text_results)
        }
        
        # Get model names
        all_models = set()
        for _, (_, results_dict) in metrics.items():
            all_models.update(results_dict.keys())
        
        model_names = list(all_models)
        
        if len(model_names) < 2:
            return ""
        
        # Prepare data
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        colors = plt.cm.Set1(np.linspace(0, 1, len(model_names)))
        
        for i, model_name in enumerate(model_names):
            values = []
            for metric_name, (metric_key, results_dict) in metrics.items():
                if model_name in results_dict and metric_key in results_dict[model_name]:
                    values.append(results_dict[model_name][metric_key])
                else:
                    values.append(0)
            
            values += values[:1]  # Complete the circle
            
            ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])
        
        # Customize chart
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics.keys())
        ax.set_ylim(0, 1)
        ax.set_title("Model Performance Comparison", size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        plt.tight_layout()
        save_path = self.viz_dir / save_name
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_binary_confusion_matrices(self, 
                                       binary_results: Dict[str, Any],
                                       save_name: str = "confusion_matrices.png") -> str:
        """
        Create confusion matrices for binary classification
        
        Args:
            binary_results: Binary evaluation results
            save_name: Name for saved file
            
        Returns:
            Path to saved visualization
        """
        model_names = list(binary_results.keys())
        
        if not model_names:
            return ""
        
        n_models = len(model_names)
        fig, axes = plt.subplots(1, n_models, figsize=(5*n_models, 4))
        
        if n_models == 1:
            axes = [axes]
        
        for i, model_name in enumerate(model_names):
            results = binary_results[model_name]
            
            # Create confusion matrix
            tp = results.get('true_positives', 0)
            tn = results.get('true_negatives', 0)
            fp = results.get('false_positives', 0)
            fn = results.get('false_negatives', 0)
            
            cm = np.array([[tn, fp], [fn, tp]])
            
            # Plot confusion matrix
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                       xticklabels=['Defective', 'OK'],
                       yticklabels=['Defective', 'OK'],
                       ax=axes[i])
            
            axes[i].set_title(f'{model_name}\nAccuracy: {results.get("binary_accuracy", 0):.3f}')
            axes[i].set_xlabel('Predicted')
            axes[i].set_ylabel('Actual')
        
        plt.tight_layout()
        save_path = self.viz_dir / save_name
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_metric_comparison_bar_chart(self, 
                                         binary_results: Dict[str, Any],
                                         text_results: Dict[str, Any],
                                         save_name: str = "metric_comparison.png") -> str:
        """
        Create bar chart comparing metrics across models
        
        Args:
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            save_name: Name for saved file
            
        Returns:
            Path to saved visualization
        """
        # Prepare data
        data = []
        
        # Binary metrics
        binary_metrics = ['binary_accuracy', 'binary_f1', 'binary_precision', 'binary_recall']
        for model_name, results in binary_results.items():
            for metric in binary_metrics:
                if metric in results:
                    data.append({
                        'Model': model_name,
                        'Metric': metric.replace('binary_', '').title(),
                        'Value': results[metric],
                        'Category': 'Binary Classification'
                    })
        
        # Text metrics
        text_metrics = ['bleu_4', 'rougeL_f1', 'meteor', 'bertscore_f1']
        for model_name, results in text_results.items():
            for metric in text_metrics:
                if metric in results:
                    data.append({
                        'Model': model_name,
                        'Metric': metric.replace('_', ' ').title(),
                        'Value': results[metric],
                        'Category': 'Text Generation'
                    })
        
        if not data:
            return ""
        
        df = pd.DataFrame(data)
        
        # Create plot
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Binary metrics
        binary_df = df[df['Category'] == 'Binary Classification']
        if not binary_df.empty:
            sns.barplot(data=binary_df, x='Metric', y='Value', hue='Model', ax=axes[0])
            axes[0].set_title('Binary Classification Metrics')
            axes[0].set_ylim(0, 1)
            axes[0].tick_params(axis='x', rotation=45)
        
        # Text metrics
        text_df = df[df['Category'] == 'Text Generation']
        if not text_df.empty:
            sns.barplot(data=text_df, x='Metric', y='Value', hue='Model', ax=axes[1])
            axes[1].set_title('Text Generation Metrics')
            axes[1].set_ylim(0, 1)
            axes[1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        save_path = self.viz_dir / save_name
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_semantic_similarity_distribution(self, 
                                              text_results: Dict[str, Any],
                                              save_name: str = "semantic_similarity_dist.png") -> str:
        """
        Create distribution plot for semantic similarity scores
        
        Args:
            text_results: Text evaluation results
            save_name: Name for saved file
            
        Returns:
            Path to saved visualization
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        for model_name, results in text_results.items():
            if 'semantic_similarity_scores' in results:
                scores = results['semantic_similarity_scores']
                ax.hist(scores, alpha=0.7, label=model_name, bins=20)
        
        ax.set_xlabel('Semantic Similarity Score')
        ax.set_ylabel('Frequency')
        ax.set_title('Distribution of Semantic Similarity Scores')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = self.viz_dir / save_name
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_per_image_performance(self, 
                                   binary_results: Dict[str, Any],
                                   text_results: Dict[str, Any],
                                   save_name: str = "per_image_performance.png") -> str:
        """
        Create per-image performance comparison
        
        Args:
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            save_name: Name for saved file
            
        Returns:
            Path to saved visualization
        """
        # Collect per-image data
        image_data = {}
        
        # Binary per-image accuracy
        for model_name, results in binary_results.items():
            if 'per_image_accuracy' in results:
                for image_name, accuracy in results['per_image_accuracy'].items():
                    if image_name not in image_data:
                        image_data[image_name] = {}
                    image_data[image_name][f'{model_name}_binary'] = accuracy
        
        # Text per-image BLEU
        for model_name, results in text_results.items():
            if 'per_image_bleu' in results:
                for image_name, bleu in results['per_image_bleu'].items():
                    if image_name not in image_data:
                        image_data[image_name] = {}
                    image_data[image_name][f'{model_name}_bleu'] = bleu
        
        if not image_data:
            return ""
        
        # Create DataFrame
        df_data = []
        for image_name, metrics in image_data.items():
            for metric_name, value in metrics.items():
                model_name, metric_type = metric_name.rsplit('_', 1)
                df_data.append({
                    'Image': image_name,
                    'Model': model_name,
                    'Metric Type': metric_type.title(),
                    'Value': value
                })
        
        df = pd.DataFrame(df_data)
        
        # Create plot
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Binary accuracy per image
        binary_df = df[df['Metric Type'] == 'Binary']
        if not binary_df.empty:
            sns.barplot(data=binary_df, x='Image', y='Value', hue='Model', ax=axes[0])
            axes[0].set_title('Binary Accuracy per Image')
            axes[0].set_ylim(0, 1)
            axes[0].tick_params(axis='x', rotation=45)
        
        # BLEU per image
        bleu_df = df[df['Metric Type'] == 'Bleu']
        if not bleu_df.empty:
            sns.barplot(data=bleu_df, x='Image', y='Value', hue='Model', ax=axes[1])
            axes[1].set_title('BLEU Score per Image')
            axes[1].set_ylim(0, 1)
            axes[1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        save_path = self.viz_dir / save_name
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_all_visualizations(self, 
                                binary_results: Dict[str, Any],
                                text_results: Dict[str, Any]) -> List[str]:
        """
        Create all visualizations
        
        Args:
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            
        Returns:
            List of paths to created visualizations
        """
        created_files = []
        
        try:
            # Performance radar chart
            radar_path = self.create_performance_radar_chart(binary_results, text_results)
            if radar_path:
                created_files.append(radar_path)
        except Exception as e:
            print(f"Warning: Failed to create radar chart: {e}")
        
        try:
            # Confusion matrices
            cm_path = self.create_binary_confusion_matrices(binary_results)
            if cm_path:
                created_files.append(cm_path)
        except Exception as e:
            print(f"Warning: Failed to create confusion matrices: {e}")
        
        try:
            # Metric comparison
            bar_path = self.create_metric_comparison_bar_chart(binary_results, text_results)
            if bar_path:
                created_files.append(bar_path)
        except Exception as e:
            print(f"Warning: Failed to create bar chart: {e}")
        
        try:
            # Semantic similarity distribution
            dist_path = self.create_semantic_similarity_distribution(text_results)
            if dist_path:
                created_files.append(dist_path)
        except Exception as e:
            print(f"Warning: Failed to create distribution plot: {e}")
        
        try:
            # Per-image performance
            image_path = self.create_per_image_performance(binary_results, text_results)
            if image_path:
                created_files.append(image_path)
        except Exception as e:
            print(f"Warning: Failed to create per-image plot: {e}")
        
        return created_files
