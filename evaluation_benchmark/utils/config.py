"""
Configuration management for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class DatasetConfig:
    """Dataset configuration"""
    test_jsonl_path: str
    test_images_dir: str


@dataclass
class ModelConfig:
    """Model configuration"""
    model_path: str
    model_type: str
    torch_dtype: str = "bfloat16"
    device_map: str = "auto"


@dataclass
class InferenceConfig:
    """Inference configuration"""
    max_new_tokens: int = 100
    do_sample: bool = False
    batch_size: int = 1
    clear_gpu_memory: bool = True


@dataclass
class MetricsConfig:
    """Metrics configuration"""
    text_metrics: Dict[str, Any]
    binary_metrics: Dict[str, Any]
    semantic_metrics: Dict[str, Any]


@dataclass
class StatisticsConfig:
    """Statistical analysis configuration"""
    confidence_level: float = 0.95
    bootstrap_samples: int = 1000
    significance_tests: list = None


@dataclass
class OutputConfig:
    """Output configuration"""
    results_dir: str
    save_individual_results: bool = True
    save_aggregate_results: bool = True
    generate_visualizations: bool = True
    export_formats: list = None


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    log_file: str = "evaluation.log"
    console_output: bool = True
    progress_bar: bool = True


@dataclass
class PerformanceConfig:
    """Performance configuration"""
    num_workers: int = 4
    gpu_memory_fraction: float = 0.8
    clear_cache_frequency: int = 10


@dataclass
class EvaluationConfig:
    """Main evaluation configuration"""
    dataset: DatasetConfig
    models: Dict[str, ModelConfig]
    inference: InferenceConfig
    metrics: MetricsConfig
    statistics: StatisticsConfig
    output: OutputConfig
    logging: LoggingConfig
    performance: PerformanceConfig


class ConfigLoader:
    """Configuration loader and validator"""
    
    @staticmethod
    def load_config(config_path: str) -> EvaluationConfig:
        """Load configuration from YAML file"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as file:
            config_dict = yaml.safe_load(file)
        
        return ConfigLoader._parse_config(config_dict)
    
    @staticmethod
    def _parse_config(config_dict: Dict[str, Any]) -> EvaluationConfig:
        """Parse configuration dictionary into dataclass"""
        
        # Parse dataset config
        dataset_config = DatasetConfig(**config_dict['dataset'])
        
        # Parse model configs
        models_config = {}
        for model_name, model_dict in config_dict['models'].items():
            models_config[model_name] = ModelConfig(**model_dict)
        
        # Parse other configs
        inference_config = InferenceConfig(**config_dict['inference'])
        metrics_config = MetricsConfig(**config_dict['metrics'])
        statistics_config = StatisticsConfig(**config_dict['statistics'])
        output_config = OutputConfig(**config_dict['output'])
        logging_config = LoggingConfig(**config_dict['logging'])
        performance_config = PerformanceConfig(**config_dict['performance'])
        
        return EvaluationConfig(
            dataset=dataset_config,
            models=models_config,
            inference=inference_config,
            metrics=metrics_config,
            statistics=statistics_config,
            output=output_config,
            logging=logging_config,
            performance=performance_config
        )
    
    @staticmethod
    def validate_config(config: EvaluationConfig) -> bool:
        """Validate configuration"""
        # Check if paths exist
        if not os.path.exists(config.dataset.test_jsonl_path):
            raise FileNotFoundError(f"Test JSONL file not found: {config.dataset.test_jsonl_path}")
        
        if not os.path.exists(config.dataset.test_images_dir):
            raise FileNotFoundError(f"Test images directory not found: {config.dataset.test_images_dir}")
        
        # Check model paths
        for model_name, model_config in config.models.items():
            if not os.path.exists(model_config.model_path):
                raise FileNotFoundError(f"Model path not found for {model_name}: {model_config.model_path}")
        
        # Create output directory if it doesn't exist
        os.makedirs(config.output.results_dir, exist_ok=True)
        
        return True


def get_default_config_path() -> str:
    """Get default configuration file path"""
    current_dir = Path(__file__).parent.parent
    return str(current_dir / "configs" / "evaluation_config.yaml")
