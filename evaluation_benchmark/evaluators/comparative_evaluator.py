"""
Comparative evaluator for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from scipy import stats
from collections import defaultdict

from evaluation_benchmark.core.data_loader import EvaluationSample
from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class ComparativeEvaluator:
    """Evaluator for comparing model performance with statistical significance testing"""
    
    def __init__(self, 
                 confidence_level: float = 0.95,
                 bootstrap_samples: int = 1000,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize comparative evaluator
        
        Args:
            confidence_level: Confidence level for statistical tests
            bootstrap_samples: Number of bootstrap samples
            logger: Logger instance
        """
        self.confidence_level = confidence_level
        self.bootstrap_samples = bootstrap_samples
        self.logger = logger
        self.alpha = 1 - confidence_level
    
    def compare_models(self, 
                      samples: List[EvaluationSample],
                      model_results: Dict[str, Dict[str, str]],
                      binary_results: Dict[str, Any],
                      text_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive model comparison with statistical testing
        
        Args:
            samples: List of evaluation samples
            model_results: Dictionary mapping model_name -> {sample_id: response}
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            
        Returns:
            Dictionary with comparison results
        """
        start_time = time.time()

        if self.logger:
            self.logger.info("Starting comprehensive model comparison")
            self.logger.info(f"Comparing {len(model_results)} models: {list(model_results.keys())}")

        model_names = list(model_results.keys())

        if len(model_names) < 2:
            if self.logger:
                self.logger.warning("Need at least 2 models for comparison")
            return {'error': 'Need at least 2 models for comparison'}

        comparison_results = {
            'model_names': model_names,
            'pairwise_comparisons': {},
            'overall_ranking': {},
            'statistical_tests': {},
            'effect_sizes': {}
        }

        # Pairwise comparisons
        total_pairs = len(model_names) * (len(model_names) - 1) // 2
        pair_count = 0

        for i, model1 in enumerate(model_names):
            for model2 in model_names[i+1:]:
                pair_count += 1
                comparison_key = f"{model1}_vs_{model2}"

                if self.logger:
                    self.logger.debug(f"Comparing pair {pair_count}/{total_pairs}: {model1} vs {model2}")

                pairwise_result = self._compare_model_pair(
                    model1, model2, samples, model_results, binary_results, text_results
                )

                comparison_results['pairwise_comparisons'][comparison_key] = pairwise_result

        # Overall ranking
        if self.logger:
            self.logger.debug("Calculating overall model ranking")
        ranking = self._calculate_overall_ranking(binary_results, text_results)
        comparison_results['overall_ranking'] = ranking

        # Statistical significance summary
        if self.logger:
            self.logger.debug("Summarizing statistical significance")
        significance_summary = self._summarize_statistical_significance(
            comparison_results['pairwise_comparisons']
        )
        comparison_results['statistical_significance_summary'] = significance_summary

        calculation_time = time.time() - start_time

        if self.logger:
            self.logger.info(f"Model comparison completed in {calculation_time:.3f}s")
            if 'best_model' in ranking:
                self.logger.info(f"Best performing model: {ranking['best_model']}")

        return comparison_results
    
    def _compare_model_pair(self, 
                           model1: str, 
                           model2: str,
                           samples: List[EvaluationSample],
                           model_results: Dict[str, Dict[str, str]],
                           binary_results: Dict[str, Any],
                           text_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare a pair of models
        
        Args:
            model1: First model name
            model2: Second model name
            samples: List of evaluation samples
            model_results: Model results
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            
        Returns:
            Dictionary with pairwise comparison results
        """
        comparison = {
            'models': [model1, model2],
            'binary_comparison': {},
            'text_comparison': {},
            'statistical_tests': {},
            'effect_sizes': {},
            'practical_significance': {}
        }
        
        # Binary comparison
        if model1 in binary_results and model2 in binary_results:
            binary_comp = self._compare_binary_performance(
                model1, model2, samples, model_results, binary_results
            )
            comparison['binary_comparison'] = binary_comp
        
        # Text comparison
        if model1 in text_results and model2 in text_results:
            text_comp = self._compare_text_performance(
                model1, model2, samples, model_results, text_results
            )
            comparison['text_comparison'] = text_comp
        
        return comparison
    
    def _compare_binary_performance(self, 
                                   model1: str, 
                                   model2: str,
                                   samples: List[EvaluationSample],
                                   model_results: Dict[str, Dict[str, str]],
                                   binary_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare binary classification performance between two models
        
        Args:
            model1: First model name
            model2: Second model name
            samples: List of evaluation samples
            model_results: Model results
            binary_results: Binary evaluation results
            
        Returns:
            Dictionary with binary comparison results
        """
        # Get binary samples
        binary_samples = [s for s in samples if s.response_type == 'binary']
        
        if not binary_samples:
            return {}
        
        # Extract predictions for both models
        model1_predictions = []
        model2_predictions = []
        references = []
        
        for sample in binary_samples:
            if (sample.sample_id in model_results[model1] and 
                sample.sample_id in model_results[model2]):
                model1_predictions.append(model_results[model1][sample.sample_id])
                model2_predictions.append(model_results[model2][sample.sample_id])
                references.append(sample.ground_truth)
        
        if not model1_predictions:
            return {}
        
        # Convert to binary labels
        from evaluation_benchmark.core.metrics_calculator import BinaryMetricsCalculator
        calculator = BinaryMetricsCalculator()
        
        model1_labels = [calculator._is_positive(pred) for pred in model1_predictions]
        model2_labels = [calculator._is_positive(pred) for pred in model2_predictions]
        ref_labels = [calculator._is_positive(ref) for ref in references]
        
        # Calculate correctness for each model
        model1_correct = [pred == ref for pred, ref in zip(model1_labels, ref_labels)]
        model2_correct = [pred == ref for pred, ref in zip(model2_labels, ref_labels)]
        
        # McNemar's test for paired binary classification
        mcnemar_result = self._mcnemar_test(model1_correct, model2_correct)
        
        # Effect size (Cohen's h for proportions)
        acc1 = sum(model1_correct) / len(model1_correct)
        acc2 = sum(model2_correct) / len(model2_correct)
        cohens_h = self._cohens_h(acc1, acc2)
        
        # Performance differences
        performance_diff = {
            'accuracy_difference': acc2 - acc1,
            'accuracy_relative_improvement': (acc2 - acc1) / acc1 if acc1 > 0 else 0,
            'f1_difference': binary_results[model2]['binary_f1'] - binary_results[model1]['binary_f1'],
            'precision_difference': binary_results[model2]['binary_precision'] - binary_results[model1]['binary_precision'],
            'recall_difference': binary_results[model2]['binary_recall'] - binary_results[model1]['binary_recall']
        }
        
        return {
            'performance_differences': performance_diff,
            'mcnemar_test': mcnemar_result,
            'effect_size_cohens_h': cohens_h,
            'sample_size': len(model1_correct),
            'better_model': model2 if acc2 > acc1 else model1
        }
    
    def _compare_text_performance(self, 
                                 model1: str, 
                                 model2: str,
                                 samples: List[EvaluationSample],
                                 model_results: Dict[str, Dict[str, str]],
                                 text_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare text generation performance between two models
        
        Args:
            model1: First model name
            model2: Second model name
            samples: List of evaluation samples
            model_results: Model results
            text_results: Text evaluation results
            
        Returns:
            Dictionary with text comparison results
        """
        # Key metrics for comparison
        key_metrics = ['bleu_4', 'rougeL_f1', 'meteor', 'bertscore_f1', 'semantic_similarity_mean']
        
        comparison = {
            'metric_differences': {},
            'statistical_tests': {},
            'effect_sizes': {},
            'better_model_per_metric': {}
        }
        
        for metric in key_metrics:
            if metric in text_results[model1] and metric in text_results[model2]:
                val1 = text_results[model1][metric]
                val2 = text_results[model2][metric]
                
                # Performance difference
                comparison['metric_differences'][metric] = {
                    'absolute_difference': val2 - val1,
                    'relative_improvement': (val2 - val1) / val1 if val1 > 0 else 0
                }
                
                # Better model for this metric
                comparison['better_model_per_metric'][metric] = model2 if val2 > val1 else model1
                
                # Effect size (Cohen's d approximation)
                # Note: This is simplified - ideally we'd need individual sample scores
                std1 = text_results[model1].get(f"{metric}_std", 0.1)  # Default std if not available
                std2 = text_results[model2].get(f"{metric}_std", 0.1)
                pooled_std = np.sqrt((std1**2 + std2**2) / 2)
                cohens_d = (val2 - val1) / pooled_std if pooled_std > 0 else 0
                
                comparison['effect_sizes'][metric] = {
                    'cohens_d': cohens_d,
                    'interpretation': self._interpret_cohens_d(cohens_d)
                }
        
        return comparison
    
    def _mcnemar_test(self, model1_correct: List[bool], model2_correct: List[bool]) -> Dict[str, Any]:
        """
        Perform McNemar's test for paired binary outcomes
        
        Args:
            model1_correct: List of correctness for model 1
            model2_correct: List of correctness for model 2
            
        Returns:
            Dictionary with test results
        """
        # Create contingency table
        both_correct = sum(1 for c1, c2 in zip(model1_correct, model2_correct) if c1 and c2)
        model1_only = sum(1 for c1, c2 in zip(model1_correct, model2_correct) if c1 and not c2)
        model2_only = sum(1 for c1, c2 in zip(model1_correct, model2_correct) if not c1 and c2)
        both_wrong = sum(1 for c1, c2 in zip(model1_correct, model2_correct) if not c1 and not c2)
        
        # McNemar's test statistic
        if model1_only + model2_only > 0:
            chi2_stat = (abs(model1_only - model2_only) - 1)**2 / (model1_only + model2_only)
            p_value = 1 - stats.chi2.cdf(chi2_stat, 1)
        else:
            chi2_stat = 0
            p_value = 1.0
        
        return {
            'chi2_statistic': chi2_stat,
            'p_value': p_value,
            'significant': p_value < self.alpha,
            'contingency_table': {
                'both_correct': both_correct,
                'model1_only_correct': model1_only,
                'model2_only_correct': model2_only,
                'both_wrong': both_wrong
            }
        }
    
    def _cohens_h(self, p1: float, p2: float) -> float:
        """
        Calculate Cohen's h for comparing proportions
        
        Args:
            p1: Proportion 1
            p2: Proportion 2
            
        Returns:
            Cohen's h value
        """
        # Avoid division by zero
        p1 = max(0.001, min(0.999, p1))
        p2 = max(0.001, min(0.999, p2))
        
        return 2 * (np.arcsin(np.sqrt(p2)) - np.arcsin(np.sqrt(p1)))
    
    def _interpret_cohens_d(self, d: float) -> str:
        """
        Interpret Cohen's d effect size
        
        Args:
            d: Cohen's d value
            
        Returns:
            Interpretation string
        """
        abs_d = abs(d)
        if abs_d < 0.2:
            return "negligible"
        elif abs_d < 0.5:
            return "small"
        elif abs_d < 0.8:
            return "medium"
        else:
            return "large"
    
    def _calculate_overall_ranking(self, 
                                  binary_results: Dict[str, Any],
                                  text_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall model ranking
        
        Args:
            binary_results: Binary evaluation results
            text_results: Text evaluation results
            
        Returns:
            Dictionary with ranking results
        """
        model_scores = defaultdict(list)
        
        # Binary metrics (if available)
        binary_metrics = ['binary_accuracy', 'binary_f1']
        for model_name, results in binary_results.items():
            for metric in binary_metrics:
                if metric in results:
                    model_scores[model_name].append(results[metric])
        
        # Text metrics (if available)
        text_metrics = ['bleu_4', 'rougeL_f1', 'meteor', 'bertscore_f1', 'semantic_similarity_mean']
        for model_name, results in text_results.items():
            for metric in text_metrics:
                if metric in results:
                    model_scores[model_name].append(results[metric])
        
        # Calculate average scores
        avg_scores = {}
        for model_name, scores in model_scores.items():
            avg_scores[model_name] = np.mean(scores) if scores else 0
        
        # Rank models
        ranked_models = sorted(avg_scores.keys(), key=lambda k: avg_scores[k], reverse=True)
        
        return {
            'average_scores': avg_scores,
            'ranking': ranked_models,
            'best_model': ranked_models[0] if ranked_models else None,
            'score_differences': {
                f"{ranked_models[i]}_vs_{ranked_models[i+1]}": 
                avg_scores[ranked_models[i]] - avg_scores[ranked_models[i+1]]
                for i in range(len(ranked_models)-1)
            } if len(ranked_models) > 1 else {}
        }
    
    def _summarize_statistical_significance(self, pairwise_comparisons: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize statistical significance across all comparisons
        
        Args:
            pairwise_comparisons: Pairwise comparison results
            
        Returns:
            Dictionary with significance summary
        """
        significant_binary = 0
        total_binary = 0
        significant_improvements = []
        
        for comparison_key, comparison in pairwise_comparisons.items():
            # Binary significance
            if 'binary_comparison' in comparison and comparison['binary_comparison']:
                total_binary += 1
                mcnemar = comparison['binary_comparison'].get('mcnemar_test', {})
                if mcnemar.get('significant', False):
                    significant_binary += 1
                    significant_improvements.append({
                        'comparison': comparison_key,
                        'type': 'binary',
                        'p_value': mcnemar.get('p_value', 1.0),
                        'better_model': comparison['binary_comparison'].get('better_model')
                    })
        
        return {
            'binary_significance_rate': significant_binary / total_binary if total_binary > 0 else 0,
            'significant_improvements': significant_improvements,
            'total_comparisons': len(pairwise_comparisons),
            'significant_comparisons': len(significant_improvements)
        }
