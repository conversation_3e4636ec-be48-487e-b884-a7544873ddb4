"""
Text generation evaluator for PaliGemma2 evaluation benchmark
Author: Johnny
"""

import time
from typing import List, Dict, Any, Optional
import numpy as np
from collections import defaultdict
from tqdm import tqdm

from evaluation_benchmark.core.data_loader import EvaluationSample
from evaluation_benchmark.core.metrics_calculator import TextMetricsCalculator
from evaluation_benchmark.core.semantic_evaluator import SemanticSimilarityEvaluator, LLMJudgeEvaluator, DomainSpecificEvaluator
from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class TextEvaluator:
    """Evaluator for text generation tasks"""
    
    def __init__(self, 
                 semantic_config: Dict[str, Any] = None,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize text evaluator
        
        Args:
            semantic_config: Configuration for semantic evaluation
            logger: Logger instance
        """
        self.logger = logger
        self.semantic_config = semantic_config or {}
        
        # Initialize calculators
        self.text_metrics_calculator = TextMetricsCalculator(logger)
        
        # Initialize semantic evaluators
        self.semantic_evaluator = None
        self.llm_judge_evaluator = None
        self.domain_evaluator = None
        
        if self.semantic_config.get('enabled', True):
            # Sentence transformer evaluator (can be disabled independently)
            if self.semantic_config.get('sentence_transformer_enabled', True):
                model_name = self.semantic_config.get('sentence_transformer_model',
                                                     'sentence-transformers/all-MiniLM-L6-v2')
                try:
                    self.semantic_evaluator = SemanticSimilarityEvaluator(model_name, logger)
                except Exception as e:
                    if logger:
                        logger.warning(f"Failed to load sentence transformer: {e}")
                    self.semantic_evaluator = None

            # LLM judge evaluator (independent of sentence transformer)
            llm_config = self.semantic_config.get('llm_judge', {})
            if llm_config.get('enabled', False):
                api_key = None
                if llm_config.get('api_key_env'):
                    import os
                    api_key = os.getenv(llm_config['api_key_env'])

                self.llm_judge_evaluator = LLMJudgeEvaluator(
                    model_name=llm_config.get('model_name', 'gpt-4o-mini'),
                    api_key=api_key,
                    api_base=llm_config.get('api_base'),  # For Ollama: "http://localhost:11434/v1"
                    max_tokens=llm_config.get('max_tokens', 200),
                    temperature=llm_config.get('temperature', 0.1),
                    logger=logger
                )

            # Domain-specific evaluator
            self.domain_evaluator = DomainSpecificEvaluator(logger)
    
    def evaluate(self,
                samples: List[EvaluationSample],
                model_results: Dict[str, Dict[str, str]],
                show_progress: bool = True) -> Dict[str, Any]:
        """
        Evaluate text generation performance

        Args:
            samples: List of evaluation samples
            model_results: Dictionary mapping model_name -> {sample_id: response}
            show_progress: Whether to show progress bar

        Returns:
            Dictionary with evaluation results
        """
        start_time = time.time()
        
        if self.logger:
            self.logger.log_metrics_calculation("text generation")
        
        # Filter detailed samples
        detailed_samples = [s for s in samples if s.response_type == 'detailed']
        
        if not detailed_samples:
            if self.logger:
                self.logger.warning("No detailed samples found for evaluation")
            return {}
        
        results = {}

        # Create progress bar for model evaluation
        model_items = list(model_results.items())
        with tqdm(
            total=len(model_items),
            desc="Text evaluation",
            unit="model",
            disable=not show_progress,
            colour="magenta",
            ncols=100
        ) as pbar:

            for model_name, model_responses in model_items:
                pbar.set_description(f"Text eval ({model_name})")

                # Extract predictions and references for detailed samples
                predictions = []
                references = []
                sample_ids = []

                for sample in detailed_samples:
                    if sample.sample_id in model_responses:
                        predictions.append(model_responses[sample.sample_id])
                        references.append(sample.ground_truth)
                        sample_ids.append(sample.sample_id)

                if not predictions:
                    if self.logger:
                        self.logger.warning(f"No predictions found for {model_name} on detailed samples")
                    pbar.update(1)
                    continue

                # Calculate text generation metrics
                model_results_dict = self._evaluate_model(predictions, references, sample_ids, detailed_samples)
                model_results_dict['sample_count'] = len(predictions)

                results[model_name] = model_results_dict

                pbar.set_postfix(
                    samples=len(predictions),
                    avg_len=f"{np.mean([len(p.split()) for p in predictions]):.1f}"
                )
                pbar.update(1)
        
        calculation_time = time.time() - start_time
        
        if self.logger:
            self.logger.log_metrics_complete("text generation", calculation_time)
        
        return results
    
    def _evaluate_model(self, 
                       predictions: List[str], 
                       references: List[str],
                       sample_ids: List[str],
                       samples: List[EvaluationSample]) -> Dict[str, Any]:
        """
        Evaluate a single model's text generation performance
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            sample_ids: List of sample IDs
            samples: List of evaluation samples
            
        Returns:
            Dictionary with evaluation metrics
        """
        results = {}
        
        # Traditional text metrics
        text_metrics = self.text_metrics_calculator.calculate_all_metrics(predictions, references)
        results.update(text_metrics)
        
        # Semantic similarity metrics
        if self.semantic_evaluator:
            semantic_metrics = self.semantic_evaluator.calculate_similarity(predictions, references)
            results.update(semantic_metrics)
        
        # LLM judge metrics
        if self.llm_judge_evaluator:
            llm_judge_metrics = self.llm_judge_evaluator.evaluate_batch(predictions, references)
            results.update(llm_judge_metrics)
        
        # Domain-specific metrics
        if self.domain_evaluator:
            domain_metrics = self.domain_evaluator.calculate_domain_metrics(predictions, references)
            results.update(domain_metrics)
        
        # Additional analysis
        additional_analysis = self._analyze_text_performance(
            predictions, references, sample_ids, samples
        )
        results.update(additional_analysis)
        
        return results
    
    def _analyze_text_performance(self, 
                                 predictions: List[str], 
                                 references: List[str],
                                 sample_ids: List[str],
                                 samples: List[EvaluationSample]) -> Dict[str, Any]:
        """
        Perform additional analysis of text generation performance
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            sample_ids: List of sample IDs
            samples: List of evaluation samples
            
        Returns:
            Dictionary with additional analysis
        """
        # Length analysis
        pred_lengths = [len(pred.split()) for pred in predictions]
        ref_lengths = [len(ref.split()) for ref in references]
        
        # Per-image analysis
        image_performance = defaultdict(lambda: {'predictions': [], 'references': []})
        
        for i, sample_id in enumerate(sample_ids):
            sample = next((s for s in samples if s.sample_id == sample_id), None)
            if sample:
                image_name = sample.image_name
                image_performance[image_name]['predictions'].append(predictions[i])
                image_performance[image_name]['references'].append(references[i])
        
        # Calculate per-image metrics (using BLEU as representative)
        per_image_bleu = {}
        for image_name, data in image_performance.items():
            if data['predictions'] and data['references']:
                image_bleu = self.text_metrics_calculator.calculate_bleu(
                    data['predictions'], data['references']
                )
                per_image_bleu[image_name] = image_bleu.get('bleu_4', 0)
        
        # Quality analysis
        quality_analysis = self._analyze_response_quality(predictions, references)
        
        return {
            'length_analysis': {
                'avg_pred_length': np.mean(pred_lengths),
                'avg_ref_length': np.mean(ref_lengths),
                'pred_length_std': np.std(pred_lengths),
                'ref_length_std': np.std(ref_lengths),
                'length_ratio': np.mean(pred_lengths) / np.mean(ref_lengths) if np.mean(ref_lengths) > 0 else 0
            },
            'per_image_bleu': per_image_bleu,
            'avg_per_image_bleu': np.mean(list(per_image_bleu.values())) if per_image_bleu else 0,
            'quality_analysis': quality_analysis
        }
    
    def _analyze_response_quality(self, predictions: List[str], references: List[str]) -> Dict[str, Any]:
        """
        Analyze response quality patterns
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with quality analysis
        """
        empty_responses = sum(1 for pred in predictions if pred.strip() == '')
        very_short_responses = sum(1 for pred in predictions if len(pred.split()) <= 3)
        very_long_responses = sum(1 for pred in predictions if len(pred.split()) > 50)
        
        # Technical term coverage
        technical_terms = ['welding', 'terminal', 'wire', 'copper', 'qualified', 'defect', 'OK']
        term_coverage = []
        
        for pred, ref in zip(predictions, references):
            ref_terms = set(term for term in technical_terms if term.lower() in ref.lower())
            pred_terms = set(term for term in technical_terms if term.lower() in pred.lower())
            
            if ref_terms:
                coverage = len(pred_terms & ref_terms) / len(ref_terms)
                term_coverage.append(coverage)
        
        return {
            'empty_responses': empty_responses,
            'empty_response_rate': empty_responses / len(predictions) if predictions else 0,
            'very_short_responses': very_short_responses,
            'very_short_response_rate': very_short_responses / len(predictions) if predictions else 0,
            'very_long_responses': very_long_responses,
            'very_long_response_rate': very_long_responses / len(predictions) if predictions else 0,
            'avg_technical_term_coverage': np.mean(term_coverage) if term_coverage else 0,
            'technical_term_coverage_std': np.std(term_coverage) if term_coverage else 0
        }
    
    def compare_models(self, 
                      samples: List[EvaluationSample],
                      model_results: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Compare text generation performance between models
        
        Args:
            samples: List of evaluation samples
            model_results: Dictionary mapping model_name -> {sample_id: response}
            
        Returns:
            Dictionary with model comparison results
        """
        # Get evaluation results for each model
        evaluation_results = self.evaluate(samples, model_results)
        
        if len(evaluation_results) < 2:
            return {'error': 'Need at least 2 models for comparison'}
        
        model_names = list(evaluation_results.keys())
        comparisons = {}
        
        # Define key metrics for comparison (updated to include all BLEU variants)
        key_metrics = ['bleu_1', 'bleu_2', 'bleu_3', 'bleu_4', 'rougeL_f1', 'meteor',
                      'bertscore_f1', 'semantic_similarity_mean', 'llm_judge_mean']
        
        # Pairwise comparisons
        for i, model1 in enumerate(model_names):
            for model2 in model_names[i+1:]:
                comparison_key = f"{model1}_vs_{model2}"
                comparison_results = {}
                
                for metric in key_metrics:
                    if metric in evaluation_results[model1] and metric in evaluation_results[model2]:
                        val1 = evaluation_results[model1][metric]
                        val2 = evaluation_results[model2][metric]
                        
                        comparison_results[f"{metric}_improvement"] = val2 - val1
                        comparison_results[f"{metric}_relative_improvement"] = (val2 - val1) / val1 if val1 > 0 else 0
                
                comparisons[comparison_key] = comparison_results
        
        # Determine best model based on average performance
        model_scores = {}
        for model_name, results in evaluation_results.items():
            scores = []
            for metric in key_metrics:
                if metric in results:
                    scores.append(results[metric])
            model_scores[model_name] = np.mean(scores) if scores else 0
        
        best_model = max(model_scores.keys(), key=lambda k: model_scores[k]) if model_scores else None
        
        return {
            'individual_results': evaluation_results,
            'comparisons': comparisons,
            'model_scores': model_scores,
            'best_model': best_model
        }
