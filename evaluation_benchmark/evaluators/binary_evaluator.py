"""
Binary classification evaluator for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from collections import defaultdict
from tqdm import tqdm

from evaluation_benchmark.core.data_loader import EvaluationSample
from evaluation_benchmark.core.metrics_calculator import BinaryMetricsCalculator
from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class BinaryEvaluator:
    """Evaluator for binary classification tasks"""
    
    def __init__(self, 
                 binary_keywords: List[str] = None,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize binary evaluator
        
        Args:
            binary_keywords: Keywords indicating positive class
            logger: Logger instance
        """
        self.binary_keywords = binary_keywords or ["OK", "ok", "qualified", "acceptable"]
        self.logger = logger
        self.metrics_calculator = BinaryMetricsCalculator(binary_keywords, logger)
    
    def evaluate(self,
                samples: List[EvaluationSample],
                model_results: Dict[str, Dict[str, str]],
                show_progress: bool = True) -> Dict[str, Any]:
        """
        Evaluate binary classification performance

        Args:
            samples: List of evaluation samples
            model_results: Dictionary mapping model_name -> {sample_id: response}
            show_progress: Whether to show progress bar

        Returns:
            Dictionary with evaluation results
        """
        start_time = time.time()
        
        if self.logger:
            self.logger.log_metrics_calculation("binary classification")
        
        # Filter binary samples
        binary_samples = [s for s in samples if s.response_type == 'binary']
        
        if not binary_samples:
            if self.logger:
                self.logger.warning("No binary samples found for evaluation")
            return {}
        
        results = {}

        # Create progress bar for model evaluation
        model_items = list(model_results.items())
        with tqdm(
            total=len(model_items),
            desc="Binary evaluation",
            unit="model",
            disable=not show_progress,
            colour="cyan",
            ncols=100
        ) as pbar:

            for model_name, model_responses in model_items:
                pbar.set_description(f"Binary eval ({model_name})")

                # Extract predictions and references for binary samples
                predictions = []
                references = []
                sample_ids = []

                for sample in binary_samples:
                    if sample.sample_id in model_responses:
                        predictions.append(model_responses[sample.sample_id])
                        references.append(sample.ground_truth)
                        sample_ids.append(sample.sample_id)

                if not predictions:
                    if self.logger:
                        self.logger.warning(f"No predictions found for {model_name} on binary samples")
                    pbar.update(1)
                    continue

                # Calculate binary metrics
                binary_metrics = self.metrics_calculator.calculate_binary_metrics(predictions, references)

                pbar.set_postfix(
                    samples=len(predictions),
                    accuracy=f"{binary_metrics.get('accuracy', 0):.3f}"
                )

                # Add detailed analysis
                detailed_analysis = self._analyze_binary_performance(
                    predictions, references, sample_ids, binary_samples
                )

                results[model_name] = {
                    **binary_metrics,
                    **detailed_analysis,
                    'sample_count': len(predictions)
                }

                pbar.update(1)
        
        calculation_time = time.time() - start_time
        
        if self.logger:
            self.logger.log_metrics_complete("binary classification", calculation_time)
        
        return results
    
    def _analyze_binary_performance(self, 
                                   predictions: List[str], 
                                   references: List[str],
                                   sample_ids: List[str],
                                   samples: List[EvaluationSample]) -> Dict[str, Any]:
        """
        Perform detailed analysis of binary performance
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            sample_ids: List of sample IDs
            samples: List of evaluation samples
            
        Returns:
            Dictionary with detailed analysis
        """
        # Convert to binary labels
        pred_labels = [self.metrics_calculator._is_positive(pred) for pred in predictions]
        ref_labels = [self.metrics_calculator._is_positive(ref) for ref in references]
        
        # Analyze by image
        image_performance = defaultdict(lambda: {'correct': 0, 'total': 0})
        
        for i, sample_id in enumerate(sample_ids):
            # Find corresponding sample
            sample = next((s for s in samples if s.sample_id == sample_id), None)
            if sample:
                image_name = sample.image_name
                is_correct = pred_labels[i] == ref_labels[i]
                
                image_performance[image_name]['total'] += 1
                if is_correct:
                    image_performance[image_name]['correct'] += 1
        
        # Calculate per-image accuracy
        per_image_accuracy = {}
        for image_name, stats in image_performance.items():
            per_image_accuracy[image_name] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        
        # Analyze error patterns
        error_analysis = self._analyze_errors(predictions, references, pred_labels, ref_labels)
        
        # Response length analysis
        response_lengths = [len(pred.split()) for pred in predictions]
        
        return {
            'per_image_accuracy': per_image_accuracy,
            'avg_per_image_accuracy': np.mean(list(per_image_accuracy.values())) if per_image_accuracy else 0,
            'error_analysis': error_analysis,
            'avg_response_length': np.mean(response_lengths),
            'response_length_std': np.std(response_lengths),
            'min_response_length': np.min(response_lengths),
            'max_response_length': np.max(response_lengths)
        }
    
    def _analyze_errors(self, 
                       predictions: List[str], 
                       references: List[str],
                       pred_labels: List[bool],
                       ref_labels: List[bool]) -> Dict[str, Any]:
        """
        Analyze error patterns in binary classification
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            pred_labels: List of predicted binary labels
            ref_labels: List of reference binary labels
            
        Returns:
            Dictionary with error analysis
        """
        false_positives = []  # Predicted OK, actually defective
        false_negatives = []  # Predicted defective, actually OK
        
        for i, (pred_label, ref_label) in enumerate(zip(pred_labels, ref_labels)):
            if pred_label and not ref_label:  # False positive
                false_positives.append({
                    'prediction': predictions[i],
                    'reference': references[i],
                    'index': i
                })
            elif not pred_label and ref_label:  # False negative
                false_negatives.append({
                    'prediction': predictions[i],
                    'reference': references[i],
                    'index': i
                })
        
        # Analyze common patterns in errors
        fp_patterns = self._extract_error_patterns([fp['prediction'] for fp in false_positives])
        fn_patterns = self._extract_error_patterns([fn['prediction'] for fn in false_negatives])
        
        return {
            'false_positives_count': len(false_positives),
            'false_negatives_count': len(false_negatives),
            'false_positive_examples': false_positives[:5],  # First 5 examples
            'false_negative_examples': false_negatives[:5],  # First 5 examples
            'false_positive_patterns': fp_patterns,
            'false_negative_patterns': fn_patterns
        }
    
    def _extract_error_patterns(self, error_texts: List[str]) -> Dict[str, int]:
        """
        Extract common patterns from error texts
        
        Args:
            error_texts: List of error text examples
            
        Returns:
            Dictionary with pattern counts
        """
        if not error_texts:
            return {}
        
        patterns = defaultdict(int)
        
        for text in error_texts:
            text_lower = text.lower()
            
            # Check for common patterns
            if 'defect' in text_lower:
                patterns['contains_defect'] += 1
            if 'problem' in text_lower:
                patterns['contains_problem'] += 1
            if 'ok' in text_lower:
                patterns['contains_ok'] += 1
            if 'qualified' in text_lower:
                patterns['contains_qualified'] += 1
            if len(text.split()) <= 3:
                patterns['very_short_response'] += 1
            if len(text.split()) > 10:
                patterns['long_response'] += 1
            if text.strip() == '':
                patterns['empty_response'] += 1
        
        return dict(patterns)
    
    def compare_models(self, 
                      samples: List[EvaluationSample],
                      model_results: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
        """
        Compare binary classification performance between models
        
        Args:
            samples: List of evaluation samples
            model_results: Dictionary mapping model_name -> {sample_id: response}
            
        Returns:
            Dictionary with model comparison results
        """
        # Get evaluation results for each model
        evaluation_results = self.evaluate(samples, model_results)
        
        if len(evaluation_results) < 2:
            return {'error': 'Need at least 2 models for comparison'}
        
        model_names = list(evaluation_results.keys())
        comparisons = {}
        
        # Pairwise comparisons
        for i, model1 in enumerate(model_names):
            for model2 in model_names[i+1:]:
                comparison_key = f"{model1}_vs_{model2}"
                
                # Calculate improvement metrics
                acc1 = evaluation_results[model1]['binary_accuracy']
                acc2 = evaluation_results[model2]['binary_accuracy']
                
                f1_1 = evaluation_results[model1]['binary_f1']
                f1_2 = evaluation_results[model2]['binary_f1']
                
                comparisons[comparison_key] = {
                    'accuracy_improvement': acc2 - acc1,
                    'f1_improvement': f1_2 - f1_1,
                    'accuracy_relative_improvement': (acc2 - acc1) / acc1 if acc1 > 0 else 0,
                    'f1_relative_improvement': (f1_2 - f1_1) / f1_1 if f1_1 > 0 else 0,
                    'better_model': model2 if acc2 > acc1 else model1
                }
        
        return {
            'individual_results': evaluation_results,
            'comparisons': comparisons,
            'best_model': max(evaluation_results.keys(), 
                            key=lambda k: evaluation_results[k]['binary_accuracy'])
        }
