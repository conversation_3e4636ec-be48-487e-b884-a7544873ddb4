#!/usr/bin/env python3
"""
Setup script for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages with fallback versions"""
    requirements_file = Path(__file__).parent / "requirements.txt"

    if not requirements_file.exists():
        print("Error: requirements.txt not found")
        return False

    # Try installing from requirements.txt first
    try:
        print("Installing requirements...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing from requirements.txt: {e}")
        print("Trying to install core packages individually...")
        return install_core_packages()

def install_core_packages():
    """Install core packages individually with fallback versions"""
    core_packages = [
        "torch",
        "transformers",
        "accelerate",
        "peft",
        "Pillow",
        "torchvision",
        "nltk",
        "rouge-score",
        "sentence-transformers",
        "scikit-learn",
        "scipy",
        "numpy",
        "pandas",
        "pyyaml",
        "matplotlib",
        "seaborn",
        "tqdm"
    ]

    # Optional packages that might fail
    optional_packages = [
        "bert-score"
    ]

    failed_packages = []

    # Install core packages
    for package in core_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✓ {package} installed")
        except subprocess.CalledProcessError:
            print(f"✗ Failed to install {package}")
            failed_packages.append(package)

    # Try optional packages
    for package in optional_packages:
        try:
            print(f"Installing optional package {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✓ {package} installed")
        except subprocess.CalledProcessError:
            print(f"⚠ Optional package {package} failed to install (this is OK)")

    if failed_packages:
        print(f"\nWarning: Failed to install: {', '.join(failed_packages)}")
        print("The evaluation system may work with reduced functionality.")
        return len(failed_packages) < len(core_packages) // 2  # Return True if less than half failed

    return True

def download_nltk_data():
    """Download required NLTK data"""
    try:
        import nltk
        print("Downloading NLTK data...")
        
        # Download required NLTK data
        nltk.download('punkt', quiet=True)
        nltk.download('wordnet', quiet=True)
        nltk.download('omw-1.4', quiet=True)
        
        print("✓ NLTK data downloaded successfully")
        return True
    except Exception as e:
        print(f"Error downloading NLTK data: {e}")
        return False

def setup_directories():
    """Create necessary directories"""
    base_dir = Path(__file__).parent
    
    directories = [
        base_dir / "results",
        base_dir / "results" / "reports",
        base_dir / "results" / "visualizations"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print("✓ Directories created successfully")

def main():
    """Main setup function"""
    print("Setting up PaliGemma2 Evaluation Benchmark...")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        print("Setup failed: Could not install requirements")
        sys.exit(1)
    
    # Download NLTK data
    if not download_nltk_data():
        print("Warning: Could not download NLTK data. Some metrics may not work.")
    
    # Setup directories
    setup_directories()
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit configs/evaluation_config.yaml with your model paths")
    print("2. Run: python run_evaluation.py")
    print("3. Check results in the results/ directory")

if __name__ == "__main__":
    main()
