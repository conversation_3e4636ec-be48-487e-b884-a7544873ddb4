"""
Model inference utilities for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import torch
import gc
import time
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from transformers import PaliGemmaProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import warnings
from tqdm import tqdm

from evaluation_benchmark.core.data_loader import EvaluationSample
from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class ModelInference:
    """Model inference handler for PaliGemma2 models"""
    
    def __init__(self,
                 model_path: str,
                 model_name: str,
                 torch_dtype: str = "bfloat16",
                 device_map: str = "auto",
                 base_model_path: Optional[str] = None,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize model inference

        Args:
            model_path: Path to model
            model_name: Name identifier for model
            torch_dtype: Torch data type
            device_map: Device mapping strategy
            base_model_path: Optional manual override for base model path (for LoRA models)
            logger: Logger instance
        """
        self.model_path = model_path
        self.model_name = model_name
        self.torch_dtype = getattr(torch, torch_dtype)
        self.device_map = device_map
        self.manual_base_model_path = base_model_path
        self.logger = logger

        self.model = None
        self.processor = None
        self.device = None
        
    def load_model(self) -> bool:
        """
        Load model and processor
        
        Returns:
            True if successful, False otherwise
        """
        try:
            start_time = time.time()
            
            if self.logger:
                self.logger.log_model_loading(self.model_name, self.model_path)
            
            # Load processor (use base model path for processor)
            base_model_path = self._get_base_model_path()
            self.processor = PaliGemmaProcessor.from_pretrained(base_model_path)
            
            # Load model
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=self.torch_dtype,
                device_map=self.device_map
            ).eval()
            
            # Get device
            self.device = next(self.model.parameters()).device

            load_time = time.time() - start_time

            if self.logger:
                # Log model info with detailed information about model type
                model_info = self.get_model_info()
                self.logger.log_model_loaded(self.model_name, load_time)

                # Log additional information for LoRA models
                if model_info["is_lora"]:
                    self.logger.info(f"Loaded LoRA model: {self.model_name}")
                    self.logger.info(f"Base model: {model_info['base_model_path']}")
                    self.logger.info(f"LoRA rank: {model_info.get('lora_r')}, alpha: {model_info.get('lora_alpha')}")

            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to load {self.model_name}: {str(e)}")
            return False
    
    def _get_base_model_path(self) -> str:
        """
        Get base model path for processor loading
        Automatically detects LoRA adapters and reads base model from adapter_config.json

        Returns:
            Base model path
        """
        # Priority 1: Manual override from configuration
        if self.manual_base_model_path:
            if self.logger:
                self.logger.info(f"Using manually specified base model: {self.manual_base_model_path}")
            return self.manual_base_model_path

        # Priority 2: Check if this is a LoRA fine-tuned model by looking for adapter_config.json
        adapter_config_path = os.path.join(self.model_path, "adapter_config.json")

        if os.path.exists(adapter_config_path):
            try:
                with open(adapter_config_path, 'r', encoding='utf-8') as f:
                    adapter_config = json.load(f)

                # Get base model path from adapter config
                base_model_path = adapter_config.get("base_model_name_or_path")

                if base_model_path:
                    if self.logger:
                        self.logger.info(f"Detected LoRA adapter. Base model: {base_model_path}")
                    return base_model_path
                else:
                    if self.logger:
                        self.logger.warning("adapter_config.json found but no base_model_name_or_path specified")

            except (json.JSONDecodeError, IOError) as e:
                if self.logger:
                    self.logger.warning(f"Failed to read adapter_config.json: {e}")

        # Priority 3: Legacy detection for backward compatibility
        if "paligemma_vqav2" in self.model_path or "finetuned" in self.model_name.lower():
            fallback_path = "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224"
            if self.logger:
                self.logger.info(f"Using legacy fallback base model: {fallback_path}")
            return fallback_path

        # Priority 4: Default - use the model path itself (for base models)
        return self.model_path

    def _detect_model_type(self) -> str:
        """
        Detect model type based on directory contents

        Returns:
            Model type: 'lora', 'finetuned', or 'base'
        """
        adapter_config_path = os.path.join(self.model_path, "adapter_config.json")

        if os.path.exists(adapter_config_path):
            return "lora"
        elif "finetuned" in self.model_name.lower() or "ft" in self.model_name.lower():
            return "finetuned"
        else:
            return "base"

    def get_model_info(self) -> Dict[str, Any]:
        """
        Get comprehensive model information

        Returns:
            Dictionary with model information
        """
        model_type = self._detect_model_type()
        base_model_path = self._get_base_model_path()

        info = {
            "model_name": self.model_name,
            "model_path": self.model_path,
            "model_type": model_type,
            "base_model_path": base_model_path,
            "is_lora": model_type == "lora"
        }

        # Add LoRA specific information if applicable
        if model_type == "lora":
            adapter_config_path = os.path.join(self.model_path, "adapter_config.json")
            try:
                with open(adapter_config_path, 'r', encoding='utf-8') as f:
                    adapter_config = json.load(f)

                info.update({
                    "lora_r": adapter_config.get("r"),
                    "lora_alpha": adapter_config.get("lora_alpha"),
                    "lora_dropout": adapter_config.get("lora_dropout"),
                    "target_modules": adapter_config.get("target_modules"),
                    "peft_type": adapter_config.get("peft_type")
                })
            except (json.JSONDecodeError, IOError):
                pass

        return info
    
    def generate_response(self, 
                         image: Image.Image, 
                         question: str,
                         max_new_tokens: int = 100,
                         do_sample: bool = False) -> str:
        """
        Generate response for image-question pair
        
        Args:
            image: PIL Image
            question: Question text
            max_new_tokens: Maximum new tokens to generate
            do_sample: Whether to use sampling
            
        Returns:
            Generated response text
        """
        if self.model is None or self.processor is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            # Prepare input with <image> prefix as in training
            prompt = f"<image>{question}"
            
            # Process inputs
            model_inputs = self.processor(
                text=prompt,
                images=image,
                return_tensors="pt"
            ).to(self.torch_dtype).to(self.device)
            
            # Generate response
            input_len = model_inputs["input_ids"].shape[-1]
            
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **model_inputs,
                    max_new_tokens=max_new_tokens,
                    do_sample=do_sample,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # Decode response
            generated_text = self.processor.batch_decode(
                generated_ids[:, input_len:], 
                skip_special_tokens=True
            )[0]
            
            return generated_text.strip()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Generation error with {self.model_name}: {str(e)}")
            return ""
    
    def batch_inference(self,
                       samples: List[EvaluationSample],
                       max_new_tokens: int = 100,
                       do_sample: bool = False,
                       clear_cache_frequency: int = 10,
                       show_progress: bool = True) -> Dict[str, str]:
        """
        Perform batch inference on samples

        Args:
            samples: List of evaluation samples
            max_new_tokens: Maximum new tokens to generate
            do_sample: Whether to use sampling
            clear_cache_frequency: Clear cache every N samples
            show_progress: Whether to show progress bar

        Returns:
            Dictionary mapping sample_id to generated response
        """
        if self.model is None or self.processor is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        results = {}
        start_time = time.time()
        
        if self.logger:
            self.logger.log_inference_start(self.model_name, len(samples))

        # Create progress bar for inference
        failed_count = 0
        with tqdm(
            total=len(samples),
            desc=f"Inference ({self.model_name})",
            unit="sample",
            disable=not show_progress,
            colour="green",
            ncols=120
        ) as pbar:

            for idx, sample in enumerate(samples):
                try:
                    # Load image if not already loaded
                    if sample.image is None:
                        from .data_loader import DataLoader
                        data_loader = DataLoader("", "")  # Dummy loader for image loading
                        sample.image = data_loader.load_image(sample.image_path)

                    # Generate response
                    response = self.generate_response(
                        image=sample.image,
                        question=sample.question,
                        max_new_tokens=max_new_tokens,
                        do_sample=do_sample
                    )

                    results[sample.sample_id] = response

                    if self.logger:
                        self.logger.log_sample_processed(sample.sample_id, self.model_name)

                    # Update progress bar with detailed info
                    success_rate = ((idx + 1 - failed_count) / (idx + 1)) * 100
                    pbar.set_postfix(
                        success=f"{success_rate:.1f}%",
                        failed=failed_count,
                        tokens=max_new_tokens
                    )

                    # Clear cache periodically
                    if (idx + 1) % clear_cache_frequency == 0:
                        self._clear_gpu_cache()
                        pbar.set_postfix_str(f"GPU cache cleared | success: {success_rate:.1f}%")

                except Exception as e:
                    failed_count += 1
                    if self.logger:
                        self.logger.log_error_sample(sample.sample_id, self.model_name, str(e))
                    results[sample.sample_id] = ""

                    # Update progress bar with error info
                    success_rate = ((idx + 1 - failed_count) / (idx + 1)) * 100
                    pbar.set_postfix(
                        success=f"{success_rate:.1f}%",
                        failed=failed_count,
                        tokens=max_new_tokens
                    )

                finally:
                    pbar.update(1)
        
        inference_time = time.time() - start_time
        
        if self.logger:
            self.logger.log_inference_complete(self.model_name, inference_time)
        
        return results
    
    def _clear_gpu_cache(self):
        """Clear GPU cache to prevent memory issues"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
    
    def unload_model(self):
        """Unload model to free memory"""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.processor is not None:
            del self.processor
            self.processor = None
        
        self._clear_gpu_cache()
        
        if self.logger:
            self.logger.info(f"Unloaded {self.model_name} model")


class MultiModelInference:
    """Handler for multiple model inference"""
    
    def __init__(self, 
                 model_configs: Dict[str, Any],
                 inference_config: Any,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize multi-model inference
        
        Args:
            model_configs: Dictionary of model configurations
            inference_config: Inference configuration
            logger: Logger instance
        """
        self.model_configs = model_configs
        self.inference_config = inference_config
        self.logger = logger
        self.models = {}
    
    def run_inference(self, samples: List[EvaluationSample], show_progress: bool = True) -> Dict[str, Dict[str, str]]:
        """
        Run inference with all models

        Args:
            samples: List of evaluation samples
            show_progress: Whether to show progress bars

        Returns:
            Dictionary mapping model_name -> {sample_id: response}
        """
        all_results = {}
        
        for model_name, model_config in self.model_configs.items():
            # Create model inference handler
            model_inference = ModelInference(
                model_path=model_config.model_path,
                model_name=model_name,
                torch_dtype=model_config.torch_dtype,
                device_map=model_config.device_map,
                # Support for manually specified base_model_path in config
                base_model_path=getattr(model_config, "base_model_path", None),
                logger=self.logger
            )
            
            # Load model
            if not model_inference.load_model():
                if self.logger:
                    self.logger.error(f"Failed to load {model_name}, skipping...")
                continue
            
            # Run inference
            results = model_inference.batch_inference(
                samples=samples,
                max_new_tokens=self.inference_config.max_new_tokens,
                do_sample=self.inference_config.do_sample,
                clear_cache_frequency=self.inference_config.clear_gpu_memory,
                show_progress=show_progress
            )
            
            all_results[model_name] = results
            
            # Unload model if configured
            if self.inference_config.clear_gpu_memory:
                model_inference.unload_model()
        
        return all_results
