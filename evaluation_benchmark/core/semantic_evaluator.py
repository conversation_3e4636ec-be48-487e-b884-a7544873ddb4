"""
Semantic similarity evaluation for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import time
import numpy as np
from typing import List, Dict, Any, Optional
import re
import os

# Sentence transformers for semantic similarity
try:
    from sentence_transformers import SentenceTransformer
    from sklearn.metrics.pairwise import cosine_similarity
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# OpenAI for LLM-as-a-judge
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Requests for Ollama API
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class SemanticSimilarityEvaluator:
    """Evaluator for semantic similarity using sentence transformers"""
    
    def __init__(self, 
                 model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize semantic similarity evaluator
        
        Args:
            model_name: Sentence transformer model name
            logger: Logger instance
        """
        self.model_name = model_name
        self.logger = logger
        self.model = None
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self.model = SentenceTransformer(model_name)
                if self.logger:
                    self.logger.info(f"Loaded sentence transformer: {model_name}")
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Failed to load sentence transformer: {e}")
        else:
            if self.logger:
                self.logger.warning("Sentence transformers not available")
    
    def calculate_similarity(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate semantic similarity scores
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with similarity metrics
        """
        if self.model is None:
            if self.logger:
                self.logger.warning("Sentence transformer model not available")
            return {}
        
        try:
            # Encode texts
            pred_embeddings = self.model.encode(predictions)
            ref_embeddings = self.model.encode(references)
            
            # Calculate cosine similarities
            similarities = []
            for pred_emb, ref_emb in zip(pred_embeddings, ref_embeddings):
                sim = cosine_similarity([pred_emb], [ref_emb])[0][0]
                similarities.append(sim)
            
            return {
                'semantic_similarity_mean': np.mean(similarities),
                'semantic_similarity_std': np.std(similarities),
                'semantic_similarity_min': np.min(similarities),
                'semantic_similarity_max': np.max(similarities),
                'semantic_similarity_scores': similarities
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Semantic similarity calculation failed: {e}")
            return {}


class LLMJudgeEvaluator:
    """LLM-as-a-judge evaluator for semantic equivalence"""

    def __init__(self,
                 model_name: str = "gpt-4o-mini",
                 api_key: Optional[str] = None,
                 api_base: Optional[str] = None,
                 max_tokens: int = 200,
                 temperature: float = 0.1,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize LLM judge evaluator

        Args:
            model_name: LLM model name (e.g., "gpt-4o-mini" for OpenAI or "qwen2.5:14b-instruct" for Ollama)
            api_key: API key for OpenAI (not needed for Ollama)
            api_base: API base URL (e.g., "http://localhost:11434/v1" for Ollama)
            max_tokens: Maximum tokens for response
            temperature: Temperature for generation
            logger: Logger instance
        """
        self.model_name = model_name
        self.api_base = api_base
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.logger = logger
        self.client = None
        self.is_ollama = api_base and "11434" in api_base  # Detect Ollama by port

        if self.is_ollama:
            # Setup for Ollama
            if REQUESTS_AVAILABLE:
                self.ollama_base_url = api_base.replace("/v1", "") if api_base.endswith("/v1") else api_base
                if self.logger:
                    self.logger.info(f"Initialized LLM judge with Ollama model {model_name} at {self.ollama_base_url}")
            else:
                if self.logger:
                    self.logger.error("Requests library not available for Ollama")
        else:
            # Setup OpenAI client
            if OPENAI_AVAILABLE and api_key:
                try:
                    client_kwargs = {"api_key": api_key}
                    if api_base:
                        client_kwargs["base_url"] = api_base
                    self.client = openai.OpenAI(**client_kwargs)
                    if self.logger:
                        self.logger.info(f"Initialized LLM judge with {model_name}")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Failed to initialize OpenAI client: {e}")
            elif not api_key and not self.is_ollama:
                if self.logger:
                    self.logger.warning("No API key provided for LLM judge")
            else:
                if self.logger:
                    self.logger.warning("OpenAI not available for LLM judge")
    
    def _create_evaluation_prompt(self, prediction: str, reference: str) -> str:
        """
        Create evaluation prompt for LLM judge
        
        Args:
            prediction: Predicted text
            reference: Reference text
            
        Returns:
            Evaluation prompt
        """
        prompt = f"""You are an expert evaluator for industrial defect detection systems. Your task is to evaluate if two technical descriptions are semantically equivalent for the purpose of defect detection.

Ground Truth: "{reference}"
Prediction: "{prediction}"

Evaluate the semantic equivalence on a scale of 0-100, where:
- 100: Completely equivalent meaning (perfect match)
- 80-99: Very similar meaning with minor differences
- 60-79: Similar meaning but some important differences
- 40-59: Partially similar but significant differences
- 20-39: Some similarity but mostly different
- 0-19: Completely different meaning

Consider:
1. Technical accuracy of defect descriptions
2. Classification consistency (OK vs defect types)
3. Quantitative measurements accuracy
4. Overall semantic meaning

Respond with only a number between 0-100."""
        
        return prompt
    
    def evaluate_pair(self, prediction: str, reference: str) -> Optional[float]:
        """
        Evaluate a single prediction-reference pair

        Args:
            prediction: Predicted text
            reference: Reference text

        Returns:
            Similarity score (0-100) or None if failed
        """
        if self.is_ollama:
            return self._evaluate_pair_ollama(prediction, reference)
        else:
            return self._evaluate_pair_openai(prediction, reference)

    def _evaluate_pair_openai(self, prediction: str, reference: str) -> Optional[float]:
        """Evaluate using OpenAI API"""
        if self.client is None:
            return None

        try:
            prompt = self._create_evaluation_prompt(prediction, reference)

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an expert evaluator for technical text similarity."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            # Extract score from response
            content = response.choices[0].message.content.strip()

            # Try to extract number
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', content)
            if score_match:
                score = float(score_match.group(1))
                return min(100, max(0, score))  # Clamp to 0-100

            return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"OpenAI LLM judge evaluation failed: {e}")
            return None

    def _evaluate_pair_ollama(self, prediction: str, reference: str) -> Optional[float]:
        """Evaluate using Ollama API"""
        if not REQUESTS_AVAILABLE:
            if self.logger:
                self.logger.error("Requests library not available for Ollama")
            return None

        try:
            prompt = self._create_evaluation_prompt(prediction, reference)

            # Ollama API request
            url = f"{self.ollama_base_url}/api/generate"
            payload = {
                "model": self.model_name,
                "prompt": f"You are an expert evaluator for technical text similarity.\n\n{prompt}",
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }

            response = requests.post(url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            content = result.get("response", "").strip()

            # Try to extract number
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', content)
            if score_match:
                score = float(score_match.group(1))
                return min(100, max(0, score))  # Clamp to 0-100

            return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"Ollama LLM judge evaluation failed: {e}")
            return None
    
    def evaluate_batch(self, predictions: List[str], references: List[str]) -> Dict[str, Any]:
        """
        Evaluate batch of predictions

        Args:
            predictions: List of predicted texts
            references: List of reference texts

        Returns:
            Dictionary with evaluation results
        """
        if not self.is_ollama and self.client is None:
            if self.logger:
                self.logger.warning("LLM judge not available")
            return {}

        if self.is_ollama and not REQUESTS_AVAILABLE:
            if self.logger:
                self.logger.warning("Requests library not available for Ollama")
            return {}

        if self.logger:
            self.logger.info(f"Starting LLM judge evaluation for {len(predictions)} samples")

        scores = []
        failed_count = 0

        for i, (pred, ref) in enumerate(zip(predictions, references)):
            score = self.evaluate_pair(pred, ref)
            if score is not None:
                scores.append(score)
            else:
                failed_count += 1

            # Log progress every 20 samples
            if self.logger and (i + 1) % 20 == 0:
                self.logger.debug(f"LLM judge progress: {i + 1}/{len(predictions)} samples processed")

        if self.logger:
            success_rate = (len(scores) / len(predictions)) * 100 if predictions else 0
            self.logger.info(f"LLM judge evaluation completed: {len(scores)}/{len(predictions)} successful ({success_rate:.1f}%)")
            if failed_count > 0:
                self.logger.warning(f"LLM judge failed on {failed_count} samples")

        if not scores:
            return {}

        return {
            'llm_judge_mean': np.mean(scores),
            'llm_judge_std': np.std(scores),
            'llm_judge_min': np.min(scores),
            'llm_judge_max': np.max(scores),
            'llm_judge_scores': scores,
            'llm_judge_failed_count': failed_count,
            'llm_judge_success_rate': len(scores) / (len(scores) + failed_count)
        }


class DomainSpecificEvaluator:
    """Domain-specific evaluator for industrial defect detection"""
    
    def __init__(self, logger: Optional[EvaluationLogger] = None):
        """
        Initialize domain-specific evaluator
        
        Args:
            logger: Logger instance
        """
        self.logger = logger
        
        # Define technical terms and their importance
        self.technical_terms = {
            'welding': ['welding', 'weld', 'soldering', 'joint'],
            'defects': ['explosion', 'offset', 'bulging', 'gap', 'crack', 'defect'],
            'measurements': ['mm', 'thickness', '2/3', 'length', 'diameter'],
            'quality': ['qualified', 'acceptable', 'OK', 'good', 'proper', 'correct'],
            'components': ['terminal', 'wire', 'copper', 'electrode', 'core', 'stack']
        }
    
    def calculate_domain_metrics(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate domain-specific metrics
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with domain-specific metrics
        """
        term_accuracy_scores = []
        measurement_accuracy_scores = []
        classification_accuracy_scores = []
        
        for pred, ref in zip(predictions, references):
            # Technical term accuracy
            term_score = self._calculate_term_accuracy(pred, ref)
            term_accuracy_scores.append(term_score)
            
            # Measurement accuracy
            measurement_score = self._calculate_measurement_accuracy(pred, ref)
            measurement_accuracy_scores.append(measurement_score)
            
            # Classification accuracy
            classification_score = self._calculate_classification_accuracy(pred, ref)
            classification_accuracy_scores.append(classification_score)
        
        return {
            'domain_term_accuracy': np.mean(term_accuracy_scores),
            'domain_measurement_accuracy': np.mean(measurement_accuracy_scores),
            'domain_classification_accuracy': np.mean(classification_accuracy_scores),
            'domain_overall_score': np.mean([
                np.mean(term_accuracy_scores),
                np.mean(measurement_accuracy_scores),
                np.mean(classification_accuracy_scores)
            ])
        }
    
    def _calculate_term_accuracy(self, prediction: str, reference: str) -> float:
        """Calculate technical term accuracy"""
        pred_lower = prediction.lower()
        ref_lower = reference.lower()
        
        total_terms = 0
        correct_terms = 0
        
        for _, terms in self.technical_terms.items():
            for term in terms:
                if term in ref_lower:
                    total_terms += 1
                    if term in pred_lower:
                        correct_terms += 1
        
        return correct_terms / total_terms if total_terms > 0 else 1.0
    
    def _calculate_measurement_accuracy(self, prediction: str, reference: str) -> float:
        """Calculate measurement accuracy with numerical tolerance"""
        # Extract numbers with units using improved regex
        measurement_pattern = r'(\d+(?:\.\d+)?)\s*(?:(mm|cm|m|%|/\d+))?'

        pred_matches = re.findall(measurement_pattern, prediction.lower())
        ref_matches = re.findall(measurement_pattern, reference.lower())

        if not ref_matches:
            return 1.0  # No measurements to compare

        # Convert to normalized format (value, unit)
        def normalize_measurements(matches):
            normalized = []
            for value_str, unit in matches:
                try:
                    value = float(value_str)
                    unit = unit or ""  # Handle cases where unit is empty
                    normalized.append((value, unit))
                except ValueError:
                    continue
            return normalized

        pred_measurements = normalize_measurements(pred_matches)
        ref_measurements = normalize_measurements(ref_matches)

        if not pred_measurements:
            return 0.0  # No valid measurements in prediction

        # Calculate accuracy with tolerance
        tolerance = 0.1  # 10% tolerance for numerical values
        matched_count = 0

        for ref_value, ref_unit in ref_measurements:
            best_match = False
            for pred_value, pred_unit in pred_measurements:
                # Check if units match (or both are empty)
                if ref_unit == pred_unit:
                    # Check if values are within tolerance
                    if ref_value == 0:
                        # For zero values, use absolute tolerance
                        if abs(pred_value - ref_value) <= 0.01:
                            best_match = True
                            break
                    else:
                        # For non-zero values, use relative tolerance
                        relative_error = abs(pred_value - ref_value) / abs(ref_value)
                        if relative_error <= tolerance:
                            best_match = True
                            break

            if best_match:
                matched_count += 1

        return matched_count / len(ref_measurements)
    
    def _calculate_classification_accuracy(self, prediction: str, reference: str) -> float:
        """Calculate classification accuracy (OK vs defect)"""
        pred_is_ok = any(term in prediction.lower() for term in ['ok', 'qualified', 'acceptable', 'good'])
        ref_is_ok = any(term in reference.lower() for term in ['ok', 'qualified', 'acceptable', 'good'])
        
        return 1.0 if pred_is_ok == ref_is_ok else 0.0
