"""
Data loading utilities for PaliGemma2 evaluation benchmark
Author: Johnny
"""

import json
import os
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from PIL import Image
from pathlib import Path
import torch
from transformers.image_utils import load_image
from tqdm import tqdm


@dataclass
class EvaluationSample:
    """Single evaluation sample"""
    sample_id: str
    image_path: str
    image_name: str
    question: str
    ground_truth: str
    response_type: str  # 'binary' or 'detailed'
    image: Optional[Image.Image] = None


class DataLoader:
    """Data loader for evaluation benchmark"""
    
    def __init__(self, 
                 jsonl_path: str, 
                 images_dir: str,
                 binary_keywords: List[str] = None):
        """
        Initialize data loader
        
        Args:
            jsonl_path: Path to JSONL metadata file
            images_dir: Directory containing images
            binary_keywords: Keywords indicating binary responses
        """
        self.jsonl_path = jsonl_path
        self.images_dir = images_dir
        self.binary_keywords = binary_keywords or ["OK", "ok", "qualified", "acceptable"]
        
        # Validate paths
        if not os.path.exists(jsonl_path):
            raise FileNotFoundError(f"JSONL file not found: {jsonl_path}")
        if not os.path.exists(images_dir):
            raise FileNotFoundError(f"Images directory not found: {images_dir}")
    
    def load_samples(self, show_progress: bool = True) -> List[EvaluationSample]:
        """
        Load all evaluation samples

        Args:
            show_progress: Whether to show progress bar

        Returns:
            List of EvaluationSample objects
        """
        samples = []

        # First pass: count total lines for progress bar
        total_lines = 0
        with open(self.jsonl_path, 'r', encoding='utf-8') as file:
            for _ in file:
                total_lines += 1

        # Second pass: load samples with progress bar
        with open(self.jsonl_path, 'r', encoding='utf-8') as file:
            progress_bar = tqdm(
                total=total_lines,
                desc="Loading samples",
                unit="sample",
                disable=not show_progress,
                ncols=100
            )

            for line_idx, line in enumerate(file):
                try:
                    data = json.loads(line.strip())
                    sample = self._create_sample(data, line_idx)
                    samples.append(sample)
                    progress_bar.set_postfix(
                        loaded=len(samples),
                        errors=line_idx + 1 - len(samples)
                    )
                except json.JSONDecodeError as e:
                    print(f"Warning: Failed to parse line {line_idx + 1}: {e}")
                except Exception as e:
                    print(f"Warning: Error processing line {line_idx + 1}: {e}")
                finally:
                    progress_bar.update(1)

            progress_bar.close()

        return samples
    
    def _create_sample(self, data: Dict[str, Any], line_idx: int) -> EvaluationSample:
        """
        Create evaluation sample from JSONL data
        
        Args:
            data: JSONL data dictionary
            line_idx: Line index for sample ID
            
        Returns:
            EvaluationSample object
        """
        # Extract required fields
        image_name = data['image']
        question = data['prefix']
        ground_truth = data['suffix']
        
        # Create sample ID
        sample_id = f"{Path(image_name).stem}_{line_idx:03d}"
        
        # Determine full image path
        image_path = os.path.join(self.images_dir, image_name)
        
        # Determine response type
        response_type = self._classify_response_type(ground_truth)
        
        return EvaluationSample(
            sample_id=sample_id,
            image_path=image_path,
            image_name=image_name,
            question=question,
            ground_truth=ground_truth,
            response_type=response_type
        )
    
    def _classify_response_type(self, response: str) -> str:
        """
        Classify response as binary or detailed
        
        Args:
            response: Ground truth response
            
        Returns:
            'binary' or 'detailed'
        """
        response_lower = response.lower().strip()
        
        # Check if response is just a binary keyword
        if response_lower in [kw.lower() for kw in self.binary_keywords]:
            return 'binary'
        
        # Check if response is very short (likely binary)
        if len(response.split()) <= 3:
            return 'binary'
        
        return 'detailed'
    
    def load_image(self, image_path: str) -> Image.Image:
        """
        Load and preprocess image
        
        Args:
            image_path: Path to image file
            
        Returns:
            PIL Image object
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")
        
        try:
            # Use transformers load_image for consistency with training
            image = load_image(image_path)
            return image
        except Exception as e:
            # Fallback to PIL
            image = Image.open(image_path).convert("RGB")
            return image
    
    def get_statistics(self, samples: List[EvaluationSample]) -> Dict[str, Any]:
        """
        Get dataset statistics
        
        Args:
            samples: List of evaluation samples
            
        Returns:
            Statistics dictionary
        """
        total_samples = len(samples)
        binary_samples = sum(1 for s in samples if s.response_type == 'binary')
        detailed_samples = sum(1 for s in samples if s.response_type == 'detailed')
        
        # Count unique images
        unique_images = len(set(s.image_name for s in samples))
        
        # Count questions per image
        image_question_counts = {}
        for sample in samples:
            if sample.image_name not in image_question_counts:
                image_question_counts[sample.image_name] = 0
            image_question_counts[sample.image_name] += 1
        
        return {
            'total_samples': total_samples,
            'binary_samples': binary_samples,
            'detailed_samples': detailed_samples,
            'unique_images': unique_images,
            'binary_ratio': binary_samples / total_samples if total_samples > 0 else 0,
            'detailed_ratio': detailed_samples / total_samples if total_samples > 0 else 0,
            'avg_questions_per_image': total_samples / unique_images if unique_images > 0 else 0,
            'image_question_counts': image_question_counts
        }
    
    def filter_samples(self, 
                      samples: List[EvaluationSample], 
                      response_type: Optional[str] = None,
                      image_names: Optional[List[str]] = None,
                      max_samples: Optional[int] = None) -> List[EvaluationSample]:
        """
        Filter samples based on criteria
        
        Args:
            samples: List of evaluation samples
            response_type: Filter by response type ('binary' or 'detailed')
            image_names: Filter by specific image names
            max_samples: Maximum number of samples to return
            
        Returns:
            Filtered list of samples
        """
        filtered = samples
        
        # Filter by response type
        if response_type:
            filtered = [s for s in filtered if s.response_type == response_type]
        
        # Filter by image names
        if image_names:
            filtered = [s for s in filtered if s.image_name in image_names]
        
        # Limit number of samples
        if max_samples:
            filtered = filtered[:max_samples]
        
        return filtered


def create_data_loader(config) -> DataLoader:
    """
    Create data loader from configuration
    
    Args:
        config: Evaluation configuration
        
    Returns:
        DataLoader instance
    """
    binary_keywords = config.metrics.binary_metrics.get('binary_keywords', 
                                                        ["OK", "ok", "qualified", "acceptable"])
    
    return DataLoader(
        jsonl_path=config.dataset.test_jsonl_path,
        images_dir=config.dataset.test_images_dir,
        binary_keywords=binary_keywords
    )
