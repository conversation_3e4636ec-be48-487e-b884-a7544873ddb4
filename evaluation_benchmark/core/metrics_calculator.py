"""
Metrics calculation for PaliGemma2 evaluation benchmark
Author: <PERSON>
"""

import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import re

# Try to import torch for BERTScore tensor operations
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Text evaluation metrics
try:
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    from nltk.translate.meteor_score import meteor_score
    import nltk
    # Download required NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')
    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet')
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from rouge_score import rouge_scorer
    ROUGE_AVAILABLE = True
except ImportError:
    ROUGE_AVAILABLE = False

try:
    from bert_score import score as bert_score
    BERTSCORE_AVAILABLE = True
except ImportError:
    BERTSCORE_AVAILABLE = False

try:
    from pycocoevalcap.cider.cider import Cider
    CIDER_AVAILABLE = True
except ImportError:
    CIDER_AVAILABLE = False

from evaluation_benchmark.utils.logging_utils import EvaluationLogger


class TextMetricsCalculator:
    """Calculator for text generation metrics"""
    
    def __init__(self, logger: Optional[EvaluationLogger] = None):
        """
        Initialize text metrics calculator
        
        Args:
            logger: Logger instance
        """
        self.logger = logger
        self.smoothing_function = SmoothingFunction().method1 if NLTK_AVAILABLE else None
        self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True) if ROUGE_AVAILABLE else None
    
    def calculate_bleu(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate BLEU scores (BLEU-1 to BLEU-4)

        Args:
            predictions: List of predicted texts
            references: List of reference texts

        Returns:
            Dictionary with BLEU scores
        """
        if not NLTK_AVAILABLE:
            if self.logger:
                self.logger.warning("NLTK not available, skipping BLEU calculation")
            return {}

        bleu_1_scores = []
        bleu_2_scores = []
        bleu_3_scores = []
        bleu_4_scores = []

        for pred, ref in zip(predictions, references):
            # Tokenize
            pred_tokens = pred.lower().split()
            ref_tokens = [ref.lower().split()]

            # Skip if either is empty
            if not pred_tokens or not ref_tokens[0]:
                bleu_1_scores.append(0.0)
                bleu_2_scores.append(0.0)
                bleu_3_scores.append(0.0)
                bleu_4_scores.append(0.0)
                continue

            try:
                # Calculate BLEU-1 to BLEU-4 with appropriate weights
                # For short texts, adjust weights to be more lenient
                text_length = len(pred_tokens)

                # BLEU-1
                bleu_1 = sentence_bleu(ref_tokens, pred_tokens, weights=(1.0, 0, 0, 0),
                                     smoothing_function=self.smoothing_function)
                bleu_1_scores.append(bleu_1)

                # BLEU-2 (only if text is long enough)
                if text_length >= 2:
                    bleu_2 = sentence_bleu(ref_tokens, pred_tokens, weights=(0.5, 0.5, 0, 0),
                                         smoothing_function=self.smoothing_function)
                    bleu_2_scores.append(bleu_2)
                else:
                    bleu_2_scores.append(bleu_1)  # Fall back to BLEU-1 for very short texts

                # BLEU-3 (only if text is long enough)
                if text_length >= 3:
                    bleu_3 = sentence_bleu(ref_tokens, pred_tokens, weights=(0.33, 0.33, 0.33, 0),
                                         smoothing_function=self.smoothing_function)
                    bleu_3_scores.append(bleu_3)
                else:
                    bleu_3_scores.append(bleu_2_scores[-1])  # Fall back to BLEU-2

                # BLEU-4 (standard)
                if text_length >= 4:
                    bleu_4 = sentence_bleu(ref_tokens, pred_tokens, weights=(0.25, 0.25, 0.25, 0.25),
                                         smoothing_function=self.smoothing_function)
                    bleu_4_scores.append(bleu_4)
                else:
                    bleu_4_scores.append(bleu_3_scores[-1])  # Fall back to BLEU-3

            except Exception as e:
                if self.logger:
                    self.logger.warning(f"BLEU calculation failed for pair: {e}")
                bleu_1_scores.append(0.0)
                bleu_2_scores.append(0.0)
                bleu_3_scores.append(0.0)
                bleu_4_scores.append(0.0)

        return {
            'bleu_1': np.mean(bleu_1_scores),
            'bleu_1_std': np.std(bleu_1_scores),
            'bleu_2': np.mean(bleu_2_scores),
            'bleu_2_std': np.std(bleu_2_scores),
            'bleu_3': np.mean(bleu_3_scores),
            'bleu_3_std': np.std(bleu_3_scores),
            'bleu_4': np.mean(bleu_4_scores),
            'bleu_4_std': np.std(bleu_4_scores)
        }
    
    def calculate_rouge(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate ROUGE scores
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with ROUGE scores
        """
        if not ROUGE_AVAILABLE:
            if self.logger:
                self.logger.warning("ROUGE not available, skipping ROUGE calculation")
            return {}
        
        rouge_scores = defaultdict(list)
        
        for pred, ref in zip(predictions, references):
            scores = self.rouge_scorer.score(ref, pred)
            
            for metric, score in scores.items():
                rouge_scores[f"{metric}_f1"].append(score.fmeasure)
                rouge_scores[f"{metric}_precision"].append(score.precision)
                rouge_scores[f"{metric}_recall"].append(score.recall)
        
        # Calculate averages
        result = {}
        for metric, scores in rouge_scores.items():
            result[metric] = np.mean(scores)
            result[f"{metric}_std"] = np.std(scores)
        
        return result
    
    def calculate_meteor(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate METEOR scores

        Args:
            predictions: List of predicted texts
            references: List of reference texts

        Returns:
            Dictionary with METEOR scores
        """
        if not NLTK_AVAILABLE:
            if self.logger:
                self.logger.warning("NLTK not available, skipping METEOR calculation")
            return {}

        meteor_scores = []

        for pred, ref in zip(predictions, references):
            try:
                # Fix parameter order: meteor_score(references, hypothesis)
                # references should be a list of tokenized reference sentences
                # hypothesis should be a tokenized prediction sentence
                pred_tokens = pred.lower().split()
                ref_tokens = ref.lower().split()

                # Skip if either is empty
                if not pred_tokens or not ref_tokens:
                    meteor_scores.append(0.0)
                    continue

                score = meteor_score([ref_tokens], pred_tokens)
                meteor_scores.append(score)
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"METEOR calculation failed for pair: {e}")
                meteor_scores.append(0.0)

        return {
            'meteor': np.mean(meteor_scores),
            'meteor_std': np.std(meteor_scores)
        }
    
    def calculate_cider(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate CIDEr scores with improved error handling

        Args:
            predictions: List of predicted texts
            references: List of reference texts

        Returns:
            Dictionary with CIDEr scores
        """
        if not CIDER_AVAILABLE:
            if self.logger:
                self.logger.warning("CIDEr not available, skipping CIDEr calculation")
            return {}

        try:
            # Filter out empty predictions and references
            valid_pairs = []
            for i, (pred, ref) in enumerate(zip(predictions, references)):
                if pred.strip() and ref.strip():
                    valid_pairs.append((i, pred.strip(), ref.strip()))

            if not valid_pairs:
                if self.logger:
                    self.logger.warning("No valid prediction-reference pairs for CIDEr calculation")
                return {'cider': 0.0, 'cider_std': 0.0}

            # Format for CIDEr
            gts = {}
            res = {}

            for i, pred, ref in valid_pairs:
                gts[i] = [ref]
                res[i] = [pred]

            cider_scorer = Cider()
            score, scores = cider_scorer.compute_score(gts, res)

            # Handle case where scores might be empty or invalid
            if isinstance(scores, (list, np.ndarray)) and len(scores) > 0:
                scores_array = np.array(scores)
                # Filter out any NaN or infinite values
                valid_scores = scores_array[np.isfinite(scores_array)]
                std_score = np.std(valid_scores) if len(valid_scores) > 0 else 0.0
            else:
                std_score = 0.0

            return {
                'cider': float(score) if np.isfinite(score) else 0.0,
                'cider_std': float(std_score)
            }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"CIDEr calculation failed: {e}")
            return {'cider': 0.0, 'cider_std': 0.0}
    
    def calculate_bertscore(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate BERTScore with improved error handling

        Args:
            predictions: List of predicted texts
            references: List of reference texts

        Returns:
            Dictionary with BERTScore metrics
        """
        if not BERTSCORE_AVAILABLE:
            if self.logger:
                self.logger.warning("BERTScore not available, skipping BERTScore calculation")
            return {}

        try:
            # Filter out empty predictions and references
            valid_predictions = []
            valid_references = []

            for pred, ref in zip(predictions, references):
                pred_clean = pred.strip() if pred else ""
                ref_clean = ref.strip() if ref else ""

                # Use placeholder for empty strings to avoid BERTScore errors
                if not pred_clean:
                    pred_clean = "[EMPTY]"
                if not ref_clean:
                    ref_clean = "[EMPTY]"

                valid_predictions.append(pred_clean)
                valid_references.append(ref_clean)

            if not valid_predictions:
                return {
                    'bertscore_precision': 0.0,
                    'bertscore_recall': 0.0,
                    'bertscore_f1': 0.0,
                    'bertscore_precision_std': 0.0,
                    'bertscore_recall_std': 0.0,
                    'bertscore_f1_std': 0.0
                }

            P, R, F1 = bert_score(valid_predictions, valid_references, lang="en", verbose=False)

            # Handle potential NaN or infinite values
            if TORCH_AVAILABLE and hasattr(P, 'isfinite'):
                P_clean = P[torch.isfinite(P)] if hasattr(P, '__len__') else P
                R_clean = R[torch.isfinite(R)] if hasattr(R, '__len__') else R
                F1_clean = F1[torch.isfinite(F1)] if hasattr(F1, '__len__') else F1
            else:
                # Fallback for numpy arrays or when torch is not available
                P_clean = P[np.isfinite(P.cpu().numpy())] if hasattr(P, 'cpu') else P
                R_clean = R[np.isfinite(R.cpu().numpy())] if hasattr(R, 'cpu') else R
                F1_clean = F1[np.isfinite(F1.cpu().numpy())] if hasattr(F1, 'cpu') else F1

            return {
                'bertscore_precision': P_clean.mean().item() if len(P_clean) > 0 else 0.0,
                'bertscore_recall': R_clean.mean().item() if len(R_clean) > 0 else 0.0,
                'bertscore_f1': F1_clean.mean().item() if len(F1_clean) > 0 else 0.0,
                'bertscore_precision_std': P_clean.std().item() if len(P_clean) > 0 else 0.0,
                'bertscore_recall_std': R_clean.std().item() if len(R_clean) > 0 else 0.0,
                'bertscore_f1_std': F1_clean.std().item() if len(F1_clean) > 0 else 0.0
            }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"BERTScore calculation failed: {e}")
            return {
                'bertscore_precision': 0.0,
                'bertscore_recall': 0.0,
                'bertscore_f1': 0.0,
                'bertscore_precision_std': 0.0,
                'bertscore_recall_std': 0.0,
                'bertscore_f1_std': 0.0
            }
    
    def calculate_all_metrics(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate all text metrics
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with all metrics
        """
        all_metrics = {}
        
        # BLEU
        bleu_metrics = self.calculate_bleu(predictions, references)
        all_metrics.update(bleu_metrics)
        
        # ROUGE
        rouge_metrics = self.calculate_rouge(predictions, references)
        all_metrics.update(rouge_metrics)
        
        # METEOR
        meteor_metrics = self.calculate_meteor(predictions, references)
        all_metrics.update(meteor_metrics)
        
        # CIDEr
        cider_metrics = self.calculate_cider(predictions, references)
        all_metrics.update(cider_metrics)
        
        # BERTScore
        bertscore_metrics = self.calculate_bertscore(predictions, references)
        all_metrics.update(bertscore_metrics)
        
        return all_metrics


class BinaryMetricsCalculator:
    """Calculator for binary classification metrics"""
    
    def __init__(self, 
                 binary_keywords: List[str] = None,
                 logger: Optional[EvaluationLogger] = None):
        """
        Initialize binary metrics calculator
        
        Args:
            binary_keywords: Keywords indicating positive class
            logger: Logger instance
        """
        self.binary_keywords = binary_keywords or ["OK", "ok", "qualified", "acceptable"]
        self.logger = logger
    
    def _is_positive(self, text: str) -> bool:
        """
        Check if text indicates positive class (no defect)
        Improved logic: prioritize negative indicators to handle mixed cases

        Args:
            text: Text to check

        Returns:
            True if positive class, False otherwise
        """
        text_lower = text.lower().strip()

        # Return False for empty text
        if not text_lower:
            return False

        # First, check for strong negative indicators (highest priority)
        strong_negative_indicators = ["defect", "problem", "issue", "fault", "ng", "not qualified",
                                    "failed", "error", "broken", "damaged", "crack", "gap"]
        for indicator in strong_negative_indicators:
            if indicator in text_lower:
                return False

        # Check for negative context patterns
        negative_patterns = [
            "not ok", "not qualified", "not acceptable", "not good",
            "no good", "not proper", "not correct", "unqualified"
        ]
        for pattern in negative_patterns:
            if pattern in text_lower:
                return False

        # Then check for positive indicators
        positive_indicators = []
        for keyword in self.binary_keywords:
            positive_indicators.append(keyword.lower())

        # Add additional positive terms
        positive_indicators.extend(["good", "proper", "correct", "normal", "pass", "passed"])

        for indicator in positive_indicators:
            if indicator in text_lower:
                return True

        # Special case: very short responses that might be implicit positive
        words = text.split()
        if len(words) <= 3:
            # Check for implicit positive short responses
            implicit_positive = ["ok", "good", "qualified", "acceptable", "yes", "pass"]
            if any(word.lower() in implicit_positive for word in words):
                return True

        # Default to negative for ambiguous cases (conservative approach for defect detection)
        return False
    
    def calculate_binary_metrics(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """
        Calculate binary classification metrics
        
        Args:
            predictions: List of predicted texts
            references: List of reference texts
            
        Returns:
            Dictionary with binary metrics
        """
        # Convert to binary labels
        pred_labels = [self._is_positive(pred) for pred in predictions]
        ref_labels = [self._is_positive(ref) for ref in references]
        
        # Calculate confusion matrix
        tp = sum(1 for p, r in zip(pred_labels, ref_labels) if p and r)
        tn = sum(1 for p, r in zip(pred_labels, ref_labels) if not p and not r)
        fp = sum(1 for p, r in zip(pred_labels, ref_labels) if p and not r)
        fn = sum(1 for p, r in zip(pred_labels, ref_labels) if not p and r)
        
        # Calculate metrics
        accuracy = (tp + tn) / (tp + tn + fp + fn) if (tp + tn + fp + fn) > 0 else 0
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'binary_accuracy': accuracy,
            'binary_precision': precision,
            'binary_recall': recall,
            'binary_f1': f1,
            'true_positives': tp,
            'true_negatives': tn,
            'false_positives': fp,
            'false_negatives': fn
        }
