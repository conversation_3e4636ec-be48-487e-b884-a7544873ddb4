# PaliGemma2 Evaluation Benchmark Configuration
# Author: Johnny

# Dataset Configuration
dataset:
  test_jsonl_path: "/home/<USER>/johnny_ws/datasets/augmented_data/meta_data/test_vlm.jsonl"
  test_images_dir: "/home/<USER>/johnny_ws/datasets/augmented_data/images"
  
# Model Configuration
models:
  # paligemma_3b_mix_448_model:
  #   model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-mix-448"
  #   model_type: "mix"
  #   torch_dtype: "bfloat16"
  #   device_map: "auto"

  paligemma_3b_mix_448_model:
    model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-mix-448"
    model_type: "base"
    torch_dtype: "bfloat16"
    device_map: "auto"

  paligemma_3b_pt_448_model:
    model_path: "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-448"
    model_type: "base"
    torch_dtype: "bfloat16"
    device_map: "auto"

  paligemma_3b_ft_448_model:
    model_path: "/home/<USER>/johnny_ws/vlm_ws/fine_tune/paligemma_custom_finetuned_448_720"
    model_type: "finetuned"
    torch_dtype: "bfloat16"
    device_map: "auto"

  # LoRA 模型示例 - 系统会自动检测LoRA适配器并读取base_model_path
  # paligemma_3b_lora_model:
  #   model_path: "/path/to/lora/adapter"
  #   torch_dtype: "bfloat16"
  #   device_map: "auto"
  #   # 可选: 手动指定base_model_path (如果不指定，会自动从adapter_config.json读取)
  #   # base_model_path: "/path/to/base/model"

  # 可以添加更多模型，如：
  # another_model:
  #   model_path: "path/to/another/model"
  #   model_type: "experimental"

# Inference Configuration
inference:
  max_new_tokens: 100
  do_sample: false
  batch_size: 1
  clear_gpu_memory: true

# Evaluation Metrics Configuration
metrics:
  # Text generation metrics
  text_metrics:
    enabled: true
    metrics: ["bleu", "rouge", "meteor", "cider", "bertscore"]
    
  # Binary classification metrics
  binary_metrics:
    enabled: true
    metrics: ["accuracy", "precision", "recall", "f1"]
    binary_keywords: ["OK", "ok", "qualified", "acceptable"]
    

# 识别规则
# 如果设置了 api_base 且包含 "11434" → 自动使用Ollama模式
# 如果设置了 api_key_env 且没有Ollama端点 → 自动使用OpenAI模式
# 如果两者都设置 → 优先使用Ollama模式

  # Semantic similarity metrics
  semantic_metrics:
    enabled: true
    sentence_transformer_enabled: true  # 暂时禁用sentence transformer以避免加载问题
    sentence_transformer_model: "sentence-transformers/all-MiniLM-L6-v2"
    similarity_threshold: 0.8
    llm_judge:
      enabled: true
      # === Ollama Configuration (Local) ===
      model_name: "qwen2.5:14b-instruct-q4_K_M"  # Ollama model name
      api_base: "http://localhost:11434"  # Ollama API endpoint

      # === 火山引擎配置 (已禁用) ===
      # model_name: "ep-20250717161541-hs5xr"  # 您的推理接入点ID
      # api_key_env: "VOLCES_API_KEY"  # 环境变量名称（不是API key值本身）
      # api_base: "https://ark.cn-beijing.volces.com/api/v3"  # 火山引擎API端点

      # === Common Settings ===
      max_tokens: 200
      temperature: 0.1

# Statistical Analysis Configuration
statistics:
  confidence_level: 0.95
  bootstrap_samples: 1000
  significance_tests: ["mcnemar", "paired_ttest", "wilcoxon"]

# Output Configuration
output:
  results_dir: "/home/<USER>/johnny_ws/vlm_ws/evaluation_benchmark/results"
  save_individual_results: true
  save_aggregate_results: true
  generate_visualizations: true
  export_formats: ["json", "csv", "html"]

# Logging Configuration
logging:
  level: "INFO"
  log_file: "evaluation.log"
  console_output: true
  progress_bar: true

# Performance Configuration
performance:
  num_workers: 4
  gpu_memory_fraction: 0.8
  clear_cache_frequency: 10  # Clear cache every N samples
