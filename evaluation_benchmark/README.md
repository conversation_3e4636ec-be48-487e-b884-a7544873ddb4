# PaliGemma2 Evaluation Benchmark

A comprehensive evaluation framework for fine-tuned PaliGemma2 models on industrial defect detection tasks.

## Features

- **Multi-metric evaluation**: BLEU, ROUGE, METEOR, CIDEr, BERTScore for text generation
- **Binary classification metrics**: Accuracy, Precision, Recall, F1-score
- **Semantic similarity assessment**: Sentence transformers and LLM-as-a-judge
- **Statistical significance testing**: <PERSON><PERSON><PERSON><PERSON><PERSON>'s test, paired t-tests, effect sizes
- **Comprehensive visualizations**: Radar charts, confusion matrices, performance comparisons
- **Flexible configuration**: YAML-based configuration system
- **Detailed reporting**: JSON, CSV, and HTML output formats

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Install optional dependencies for CIDEr metric:
```bash
# For CIDEr metric (optional)
pip install git+https://github.com/salaniz/pycocoevalcap.git
```

3. Download NLTK data:
```python
import nltk
nltk.download('punkt')
nltk.download('wordnet')
```

## Quick Start

1. **Configure evaluation settings**:
   Edit `configs/evaluation_config.yaml` to set your model paths and dataset locations.

2. **Run evaluation**:
```bash
python main_evaluation.py
```

3. **Custom configuration**:
```bash
python main_evaluation.py --config my_config.yaml --output-dir ./my_results
```

## Configuration

### Dataset Configuration
```yaml
dataset:
  test_jsonl_path: "/path/to/test_vlm.jsonl"
  test_images_dir: "/path/to/test/images"
```

### Model Configuration
```yaml
models:
  base_model:
    model_path: "/path/to/base/model"
    model_type: "base"
    torch_dtype: "bfloat16"
  
  finetuned_model:
    model_path: "/path/to/finetuned/model"
    model_type: "finetuned"
    torch_dtype: "bfloat16"
```

### Metrics Configuration
```yaml
metrics:
  text_metrics:
    enabled: true
    metrics: ["bleu", "rouge", "meteor", "cider", "bertscore"]
  
  binary_metrics:
    enabled: true
    binary_keywords: ["OK", "ok", "qualified", "acceptable"]
  
  semantic_metrics:
    enabled: true
    sentence_transformer_model: "sentence-transformers/all-MiniLM-L6-v2"
    llm_judge:
      enabled: true
      model_name: "gpt-4o-mini"
      api_key_env: "OPENAI_API_KEY"
```

## Architecture

```
evaluation_benchmark/
├── core/                    # Core functionality
│   ├── data_loader.py      # Data loading and preprocessing
│   ├── model_inference.py  # Model inference handling
│   ├── metrics_calculator.py # Text and binary metrics
│   └── semantic_evaluator.py # Semantic similarity evaluation
├── evaluators/             # Specialized evaluators
│   ├── binary_evaluator.py    # Binary classification evaluation
│   ├── text_evaluator.py      # Text generation evaluation
│   └── comparative_evaluator.py # Model comparison
├── utils/                  # Utilities
│   ├── config.py          # Configuration management
│   ├── logging_utils.py   # Logging and progress tracking
│   └── visualization.py   # Visualization generation
├── configs/               # Configuration files
└── results/              # Output directory
    ├── reports/          # Generated reports
    └── visualizations/   # Generated charts
```

## Evaluation Metrics

### Binary Classification
- **Accuracy**: Overall correctness
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall

### Text Generation
- **BLEU-1 to BLEU-4**: N-gram overlap with adaptive weighting for short texts
- **ROUGE-L**: Longest common subsequence
- **METEOR**: Handles synonyms and paraphrasing (fixed parameter order)
- **CIDEr**: Consensus-based metric with improved error handling
- **BERTScore**: Semantic similarity using BERT embeddings

### Semantic Similarity
- **Sentence-BERT**: Cosine similarity of sentence embeddings
- **LLM-as-a-Judge**: GPT-4/Ollama evaluation of semantic equivalence
- **Domain-specific**: Technical term accuracy and measurement precision with numerical tolerance

## Output Formats

### JSON Report
Complete evaluation results with all metrics and individual sample results.

### CSV Summary
Tabular format suitable for further analysis in spreadsheet applications.

### HTML Report
Human-readable report with formatted tables and summaries.

### Visualizations
- Performance radar charts
- Confusion matrices
- Metric comparison bar charts
- Semantic similarity distributions
- Per-image performance analysis

## Recent Improvements

### Metrics Calculation Enhancements
- **Improved Binary Classification**: Prioritizes negative indicators, handles mixed cases better
- **Enhanced BLEU Calculation**: Now reports BLEU-1 to BLEU-4 with adaptive weighting
- **Fixed METEOR Implementation**: Corrected parameter order for accurate scoring
- **Robust Measurement Accuracy**: Numerical tolerance for measurement comparisons
- **Better Error Handling**: Improved stability across all metrics

See [METRICS_IMPROVEMENTS.md](METRICS_IMPROVEMENTS.md) for detailed information.

### Testing
Run the metrics validation:
```bash
python test_metrics_fixes.py
```

## Statistical Analysis

- **McNemar's Test**: For binary classification comparison
- **Paired t-tests**: For continuous metrics
- **Effect Sizes**: Cohen's d and Cohen's h
- **Bootstrap Confidence Intervals**: For robust estimation

## Usage Examples

### Basic Evaluation
```python
from main_evaluation import PaliGemmaEvaluationBenchmark

benchmark = PaliGemmaEvaluationBenchmark("configs/evaluation_config.yaml")
results = benchmark.run_evaluation()
```

### Custom Evaluation
```python
# Load and filter samples
samples = benchmark.data_loader.load_samples()
binary_samples = benchmark.data_loader.filter_samples(samples, response_type='binary')

# Run inference on subset
model_results = benchmark.model_inference.run_inference(binary_samples)

# Evaluate specific metrics
binary_results = benchmark.binary_evaluator.evaluate(binary_samples, model_results)
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce batch size in configuration
   - Enable GPU memory clearing
   - Use smaller models for testing

2. **Missing Dependencies**:
   - Install all requirements: `pip install -r requirements.txt`
   - For CIDEr: Install pycocoevalcap manually

3. **Model Loading Errors**:
   - Verify model paths in configuration
   - Ensure models are compatible with transformers version
   - Check device availability (CUDA/CPU)

4. **LLM Judge Issues**:
   - For Ollama: Ensure Ollama is running (`ollama serve`)
   - For OpenAI: Set `OPENAI_API_KEY` environment variable
   - Or disable LLM judge in configuration

### Performance Optimization

- Use `clear_gpu_memory: true` for memory-constrained environments
- Adjust `clear_cache_frequency` based on available memory
- Disable unused metrics to speed up evaluation
- Use smaller sentence transformer models for faster semantic evaluation

## LLM Judge Setup (Ollama)

For local LLM judge evaluation using Ollama:

### Quick Setup
```bash
# Run the automated setup script
./setup_ollama.sh
```

### Manual Setup
```bash
# 1. Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. Start Ollama service
ollama serve

# 3. Download recommended model (for 24GB VRAM)
ollama pull qwen2.5:14b-instruct-q4_K_M

# 4. Test integration
python test_ollama_integration.py
```

### Configuration
Update `configs/evaluation_config.yaml`:
```yaml
llm_judge:
  enabled: true
  model_name: "qwen2.5:14b-instruct-q4_K_M"
  api_base: "http://localhost:11434"
```

For detailed setup instructions, see [OLLAMA_SETUP.md](OLLAMA_SETUP.md).

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Citation

If you use this evaluation framework in your research, please cite:

```bibtex
@software{paligemma2_evaluation_benchmark,
  title={PaliGemma2 Evaluation Benchmark for Industrial Defect Detection},
  author={Johnny},
  year={2025},
  url={https://github.com/your-repo/paligemma2-evaluation-benchmark}
}
```
