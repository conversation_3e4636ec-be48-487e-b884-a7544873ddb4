# Core dependencies
torch>=1.12.0
transformers>=4.30.0
accelerate>=0.18.0
peft>=0.3.0

# Image processing
Pillow>=8.0.0
torchvision>=0.13.0

# Text evaluation metrics
nltk>=3.7
rouge-score>=0.1.2
bert-score>=0.3.10

# Semantic similarity
sentence-transformers>=2.0.0
scikit-learn>=1.0.0

# Statistical analysis
scipy>=1.8.0
numpy>=1.21.0

# Data handling
pandas>=1.5.0
pyyaml>=5.4.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Progress tracking
tqdm>=4.60.0

# HTTP requests (for Ollama API)
requests>=2.25.0

# LLM API support
openai>=1.0.0  # For OpenAI API compatibility

# Optional: CIDEr metric (requires manual installation)
pycocoevalcap  # Install manually if needed
