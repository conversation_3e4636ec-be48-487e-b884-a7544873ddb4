# Ollama LLM Judge Setup Guide

This guide explains how to set up and use Ollama for local LLM judge evaluation in the PaliGemma2 evaluation benchmark.

## Prerequisites

1. **GPU Requirements**: 24GB VRAM (recommended for Qwen2.5-14B)
2. **Ollama Installation**: Follow the installation guide below
3. **Python Dependencies**: Install updated requirements

## Installation Steps

### 1. Install Ollama

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Verify installation
ollama --version
```

### 2. Install Python Dependencies

```bash
cd /home/<USER>/johnny_ws/smol-vision/johnny-scripts/evaluation_benchmark
pip install -r requirements.txt
```

### 3. Download Recommended Model

```bash
# Download Qwen2.5-14B with Q4 quantization (recommended for 24GB VRAM)
ollama pull qwen2.5:14b-instruct-q4_K_M

# Alternative models (if you want to try different sizes):
# ollama pull llama3.1:8b-instruct-q5_K_M  # Smaller, safer option
# ollama pull qwen2.5:32b-instruct-q3_K_M  # Larger, higher risk
```

### 4. Start Ollama Service

```bash
# Start Ollama server (keep this running)
ollama serve
```

### 5. Test Integration

```bash
# Run the test script to verify everything works
python test_ollama_integration.py
```

## Configuration

### Update evaluation_config.yaml

The configuration has been updated to use Ollama by default:

```yaml
semantic_metrics:
  enabled: true
  sentence_transformer_model: "/home/<USER>/johnny_ws/models/all-MiniLM-L6-v2"
  similarity_threshold: 0.8
  llm_judge:
    enabled: true
    model_name: "qwen2.5:14b-instruct-q4_K_M"  # Ollama model name
    api_base: "http://localhost:11434"  # Ollama API endpoint
    max_tokens: 200
    temperature: 0.1
```

### Alternative Configurations

For different models or setups:

```yaml
# For smaller model (safer for memory)
llm_judge:
  enabled: true
  model_name: "llama3.1:8b-instruct-q5_K_M"
  api_base: "http://localhost:11434"

# For OpenAI API (if you prefer)
llm_judge:
  enabled: true
  model_name: "gpt-4o-mini"
  api_key_env: "OPENAI_API_KEY"
  # api_base: null  # Use default OpenAI endpoint
```

## Usage

### Run Full Evaluation

```bash
# Run complete evaluation with LLM judge
python main_evaluation.py --config configs/evaluation_config.yaml
```

### Run Test Evaluation

```bash
# Run on a subset for testing
python example_usage.py
```

## Memory Management

### Monitor GPU Usage

```bash
# Monitor GPU memory usage during evaluation
nvidia-smi -l 1
```

### Expected Memory Usage

With 24GB VRAM:
- **PaliGemma2-3B**: ~8-10GB
- **Sentence Transformer**: ~200MB
- **Qwen2.5-14B (Q4)**: ~9-10GB
- **System overhead**: ~1-2GB
- **Total**: ~18-22GB (safe margin)

## Troubleshooting

### Common Issues

1. **Ollama not responding**:
   ```bash
   # Check if Ollama is running
   curl http://localhost:11434/api/tags
   
   # Restart Ollama
   pkill ollama
   ollama serve
   ```

2. **Model not found**:
   ```bash
   # List available models
   ollama list
   
   # Pull missing model
   ollama pull qwen2.5:14b-instruct-q4_K_M
   ```

3. **GPU memory issues**:
   - Use smaller model: `llama3.1:8b-instruct-q5_K_M`
   - Reduce batch size in config
   - Enable more frequent cache clearing

4. **Slow evaluation**:
   - Check GPU utilization with `nvidia-smi`
   - Ensure Ollama is using GPU: `ollama ps`
   - Consider using Q3 quantization for faster inference

### Performance Tips

1. **Warm up the model**:
   ```bash
   # Send a test query to warm up
   ollama run qwen2.5:14b-instruct-q4_K_M "Hello"
   ```

2. **Optimize Ollama settings**:
   ```bash
   # Set environment variables for better performance
   export OLLAMA_NUM_PARALLEL=1
   export OLLAMA_MAX_LOADED_MODELS=1
   ```

## Model Recommendations

| Model | VRAM Usage | Performance | Speed | Recommendation |
|-------|------------|-------------|-------|----------------|
| llama3.1:8b-instruct-q5_K_M | ~6GB | Good | Fast | Safe choice |
| qwen2.5:14b-instruct-q4_K_M | ~9GB | Excellent | Medium | **Recommended** |
| qwen2.5:32b-instruct-q3_K_M | ~16GB | Best | Slow | High-end option |

## Support

If you encounter issues:

1. Run the test script: `python test_ollama_integration.py`
2. Check Ollama logs: `ollama logs`
3. Verify model availability: `ollama list`
4. Monitor system resources: `nvidia-smi` and `htop`

For more information, see:
- [Ollama Documentation](https://ollama.ai/docs)
- [Qwen2.5 Model Card](https://ollama.ai/library/qwen2.5)
