#!/usr/bin/env python3
"""
视频截取脚本 - 截取视频倒数1.5秒的内容
从 /home/<USER>/wolong_ws/finetune_data_prepare_ws/data/long_videos 目录下的所有视频文件
截取倒数1.5秒的内容，保存到对应的 /home/<USER>/wolong_ws/finetune_data_prepare_ws/data/short_videos 目录
"""

import os
import subprocess
import sys
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shorten_video.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 定义路径
BASE_DIR = Path("/home/<USER>/wolong_ws/finetune_data_prepare_ws")
LONG_VIDEOS_DIR = BASE_DIR / "data" / "long_videos"
SHORT_VIDEOS_DIR = BASE_DIR / "data" / "short_videos"

# 支持的视频格式
VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm'}

def get_video_duration(video_path):
    """获取视频时长（秒）"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1', str(video_path)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        duration = float(result.stdout.strip())
        return duration
    except (subprocess.CalledProcessError, ValueError) as e:
        logger.error(f"获取视频时长失败 {video_path}: {e}")
        return None

def extract_last_seconds(input_path, output_path, duration_seconds=1.5):
    """截取视频的倒数指定秒数"""
    try:
        # 获取视频总时长
        total_duration = get_video_duration(input_path)
        if total_duration is None:
            return False
        
        # 如果视频时长小于等于1.5秒，直接复制整个视频
        if total_duration <= duration_seconds:
            logger.warning(f"视频时长 {total_duration:.2f}s 小于等于 {duration_seconds}s，复制整个视频: {input_path}")
            start_time = 0
        else:
            # 计算开始时间（倒数1.5秒）
            start_time = total_duration - duration_seconds
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用FFmpeg截取视频
        cmd = [
            'ffmpeg', '-i', str(input_path),
            '-ss', str(start_time),
            '-t', str(duration_seconds),
            '-c', 'copy',  # 使用流复制，避免重新编码
            '-avoid_negative_ts', 'make_zero',
            '-y',  # 覆盖输出文件
            str(output_path)
        ]
        
        logger.info(f"处理视频: {input_path.name}")
        logger.info(f"总时长: {total_duration:.2f}s, 开始时间: {start_time:.2f}s")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # 验证输出文件是否创建成功
        if output_path.exists() and output_path.stat().st_size > 0:
            logger.info(f"成功创建: {output_path}")
            return True
        else:
            logger.error(f"输出文件创建失败或为空: {output_path}")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg处理失败 {input_path}: {e}")
        logger.error(f"FFmpeg错误输出: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"处理视频时发生错误 {input_path}: {e}")
        return False

def process_videos():
    """处理所有视频文件"""
    if not LONG_VIDEOS_DIR.exists():
        logger.error(f"源目录不存在: {LONG_VIDEOS_DIR}")
        return
    
    # 创建输出目录
    SHORT_VIDEOS_DIR.mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    total_videos = 0
    successful_videos = 0
    failed_videos = 0
    
    # 遍历所有子目录和文件
    for root, dirs, files in os.walk(LONG_VIDEOS_DIR):
        root_path = Path(root)
        
        # 计算相对路径，用于创建对应的输出目录结构
        relative_path = root_path.relative_to(LONG_VIDEOS_DIR)
        output_dir = SHORT_VIDEOS_DIR / relative_path
        
        for file in files:
            file_path = root_path / file
            
            # 检查是否为视频文件
            if file_path.suffix.lower() not in VIDEO_EXTENSIONS:
                logger.info(f"跳过非视频文件: {file_path}")
                continue
            
            total_videos += 1
            
            # 构建输出文件路径
            output_file_path = output_dir / file
            
            # 处理视频
            if extract_last_seconds(file_path, output_file_path):
                successful_videos += 1
            else:
                failed_videos += 1
    
    # 输出统计信息
    logger.info("=" * 50)
    logger.info("处理完成统计:")
    logger.info(f"总视频数: {total_videos}")
    logger.info(f"成功处理: {successful_videos}")
    logger.info(f"处理失败: {failed_videos}")
    logger.info("=" * 50)

def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        logger.info("FFmpeg和FFprobe检查通过")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("FFmpeg或FFprobe未找到，请确保已安装FFmpeg")
        return False

def main():
    """主函数"""
    logger.info("开始视频截取任务")
    
    # 检查FFmpeg
    if not check_ffmpeg():
        sys.exit(1)
    
    # 检查源目录
    if not LONG_VIDEOS_DIR.exists():
        logger.error(f"源目录不存在: {LONG_VIDEOS_DIR}")
        sys.exit(1)
    
    # 处理视频
    process_videos()
    
    logger.info("视频截取任务完成")

if __name__ == "__main__":
    main()
