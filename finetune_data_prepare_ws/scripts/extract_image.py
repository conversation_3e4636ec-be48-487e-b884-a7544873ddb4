#!/usr/bin/env python3
"""
视频图像提取脚本
从视频文件中提取倒数第0.5秒的图像帧
"""

import os
import cv2
import argparse
from pathlib import Path
from typing import List, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_video_files(video_dir: str) -> List[Tuple[str, str]]:
    """
    获取所有视频文件及其所属的子文件夹

    Args:
        video_dir: 视频目录路径

    Returns:
        List of (video_file_path, subfolder_name) tuples
    """
    video_files = []
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm'}

    video_path = Path(video_dir)
    if not video_path.exists():
        logger.error(f"视频目录不存在: {video_dir}")
        return video_files

    # 遍历所有子文件夹
    for subfolder in video_path.iterdir():
        if subfolder.is_dir():
            subfolder_name = subfolder.name
            logger.info(f"处理子文件夹: {subfolder_name}")

            # 遍历子文件夹中的所有文件
            for file_path in subfolder.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in video_extensions:
                    video_files.append((str(file_path), subfolder_name))
                    logger.info(f"找到视频文件: {file_path.name}")

    return video_files

def extract_frame_at_time(video_path: str, time_from_end: float = 0.5) -> Tuple[bool, any]:
    """
    从视频中提取指定时间点的帧

    Args:
        video_path: 视频文件路径
        time_from_end: 从视频结尾倒数的时间（秒）

    Returns:
        Tuple of (success, frame)
    """
    try:
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return False, None

        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps

        logger.info(f"视频信息 - FPS: {fps:.2f}, 总帧数: {frame_count}, 时长: {duration:.2f}秒")

        # 计算目标时间点
        target_time = max(0, duration - time_from_end)
        target_frame = int(target_time * fps)

        logger.info(f"目标时间点: {target_time:.2f}秒, 目标帧: {target_frame}")

        # 跳转到目标帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

        # 读取帧
        ret, frame = cap.read()
        cap.release()

        if ret:
            logger.info(f"成功提取帧")
            return True, frame
        else:
            logger.error(f"无法读取目标帧")
            return False, None

    except Exception as e:
        logger.error(f"提取帧时发生错误: {str(e)}")
        return False, None

def save_frame(frame, output_path: str) -> bool:
    """
    保存帧到文件

    Args:
        frame: OpenCV图像帧
        output_path: 输出文件路径

    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存图像
        success = cv2.imwrite(output_path, frame)
        if success:
            logger.info(f"图像已保存到: {output_path}")
            return True
        else:
            logger.error(f"保存图像失败: {output_path}")
            return False

    except Exception as e:
        logger.error(f"保存图像时发生错误: {str(e)}")
        return False

def process_videos(video_dir: str, output_base_dir: str, time_from_end: float = 0.5):
    """
    处理所有视频文件

    Args:
        video_dir: 视频目录路径
        output_base_dir: 输出基础目录路径
        time_from_end: 从视频结尾倒数的时间（秒）
    """
    # 获取所有视频文件
    video_files = get_video_files(video_dir)

    if not video_files:
        logger.warning("未找到任何视频文件")
        return

    logger.info(f"总共找到 {len(video_files)} 个视频文件")

    # 统计信息
    success_count = 0
    failed_count = 0

    # 处理每个视频文件
    for video_path, subfolder_name in video_files:
        logger.info(f"处理视频: {video_path}")

        # 提取帧
        success, frame = extract_frame_at_time(video_path, time_from_end)

        if success and frame is not None:
            # 生成输出文件名
            video_filename = Path(video_path).stem
            output_dir = os.path.join(output_base_dir, f"images_{subfolder_name}")
            output_path = os.path.join(output_dir, f"{video_filename}.jpg")

            # 保存帧
            if save_frame(frame, output_path):
                success_count += 1
            else:
                failed_count += 1
        else:
            logger.error(f"提取帧失败: {video_path}")
            failed_count += 1

    # 输出统计信息
    logger.info(f"处理完成 - 成功: {success_count}, 失败: {failed_count}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从视频文件中提取倒数第0.5秒的图像帧')
    parser.add_argument('--video_dir', type=str,
                       default='/home/<USER>/wolong_ws/finetune_data_prepare_ws/data/videos',
                       help='视频文件目录路径')
    parser.add_argument('--output_dir', type=str,
                       default='/home/<USER>/wolong_ws/finetune_data_prepare_ws/data',
                       help='输出图像目录路径')
    parser.add_argument('--time_from_end', type=float, default=0.5,
                       help='从视频结尾倒数的时间（秒）')

    args = parser.parse_args()

    logger.info(f"开始处理视频文件...")
    logger.info(f"视频目录: {args.video_dir}")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"提取时间点: 倒数第{args.time_from_end}秒")

    # 处理视频
    process_videos(args.video_dir, args.output_dir, args.time_from_end)

    logger.info("处理完成!")

if __name__ == "__main__":
    main()