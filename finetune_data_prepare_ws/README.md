# 视频帧提取工具

基于DINOv2预训练视觉编码器的视频帧提取工具，用于从视频中提取与模板图片最相似的帧。

## 功能特点

- 🎯 **智能帧选择**: 使用DINOv2预训练模型进行特征提取和相似度计算
- 📁 **自动分类**: 根据视频路径自动确定对应的模板类型
- ⚡ **高效处理**: 支持GPU加速，批量处理多个视频
- 📊 **进度显示**: 实时显示处理进度和统计信息
- 🛡️ **错误处理**: 完善的异常处理和日志记录

## 目录结构

```
data/
├── template_images/          # 模板图片目录
│   ├── ok.png               # 正常状态模板
│   ├── nok_appearance.png   # 外观异常模板
│   └── nok_ele.png          # 电气异常模板
├── short_videos/            # 输入视频目录
│   ├── segmented_videos_ok/
│   ├── segmented_videos_nok_apperence/
│   └── segmented_videos_nok_ele/
└── shotted_images/          # 输出图片目录（自动创建）
    ├── ok/
    ├── nok_appearance/
    └── nok_ele/
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
python extract_similar_frames.py
```

### 自定义参数

```bash
python extract_similar_frames.py \
    --template_dir data/template_images \
    --video_dir data/short_videos \
    --output_dir data/shotted_images \
    --device cuda \
    --num_frames 30
```

### 参数说明

- `--template_dir`: 模板图片目录路径（默认: `data/template_images`）
- `--video_dir`: 视频文件目录路径（默认: `data/short_videos`）
- `--output_dir`: 输出目录路径（默认: `data/shotted_images`）
- `--device`: 计算设备，可选 `cuda`、`cpu`、`auto`（默认: `auto`）
- `--num_frames`: 每个视频提取的帧数（默认: 30）

## 工作原理

1. **模板加载**: 加载三种类型的模板图片并提取DINOv2特征
2. **视频处理**: 对每个视频均匀提取30帧
3. **特征比较**: 计算每帧与对应模板的余弦相似度
4. **最佳选择**: 选择相似度最高的帧
5. **结果保存**: 将最佳帧保存到对应的输出目录

## 技术细节

- **视觉编码器**: Facebook DINOv2-base模型
- **相似度计算**: 余弦相似度
- **特征维度**: 768维向量
- **支持格式**: MP4, AVI, MOV, MKV, FLV, WMV
- **输出格式**: JPEG (质量95%)

## 日志文件

程序运行时会生成 `frame_extraction.log` 日志文件，记录详细的处理信息和错误。

## 注意事项

1. 首次运行时会自动下载DINOv2模型（约1.3GB）
2. 建议使用GPU加速以提高处理速度
3. 确保有足够的磁盘空间存储输出图片
4. 视频文件路径中的关键词用于自动分类：
   - `segmented_videos_ok` → ok模板
   - `segmented_videos_nok_apperence` → nok_appearance模板  
   - `segmented_videos_nok_ele` → nok_ele模板

## 故障排除

### 常见问题

1. **CUDA内存不足**: 使用 `--device cpu` 参数
2. **模型下载失败**: 检查网络连接，或使用代理
3. **视频无法打开**: 检查视频文件是否损坏
4. **权限错误**: 确保对输出目录有写权限

### 性能优化

- 使用GPU可显著提高处理速度
- 减少 `--num_frames` 参数可加快处理但可能影响准确性
- 确保有足够的内存避免频繁的磁盘交换
