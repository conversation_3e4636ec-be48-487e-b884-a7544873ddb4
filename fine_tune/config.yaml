# PaliGemma Fine-tuning Configuration
# Author: Johnny

# Model Configuration
model:
  model_id: "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-448"
  torch_dtype: "bfloat16"  # Options: float16, bfloat16, float32
  device: "cuda"  # Options: auto, cuda, cpu

  # Vision Transformer Freezing Configuration
  # 冻结视觉编码器参数，可以减少训练参数量并提高训练稳定性, 论文推荐false
  freeze_vit: false  # Options: true, false

  # Language Model Dropout Configuration
  # 语言模型部分的dropout配置，用于防止过拟合
  llm_dropout:
    # 注意力层dropout率 论文推荐值： 0.0, 0.1, 0.3
    attention_dropout: 0.1
    # 隐藏层dropout率，论文推荐值： 0.0, 0.1, 0.3
    hidden_dropout: 0.1

# LoRA Configuration
lora:
  r: 8
  lora_alpha: 32
  target_modules:
    - "q_proj"
    - "k_proj" 
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"
  task_type: "CAUSAL_LM"
  lora_dropout: 0.1

# Dataset Configuration
dataset:
  train:
    jsonl_file_path: "/home/<USER>/johnny_ws/datasets/augmented_data/meta_data/train_vlm.jsonl"
    image_directory_path: "/home/<USER>/johnny_ws/datasets/augmented_data/images/"
  
  # Optional validation dataset
  validation:
    enabled: true
    jsonl_file_path: "/home/<USER>/johnny_ws/datasets/augmented_data/meta_data/val_vlm.jsonl"
    image_directory_path: "/home/<USER>/johnny_ws/datasets/augmented_data/images/"
  
  # Image preprocessing
  image_transform:
    resize: [448, 448]
    convert_rgb: true

# Training Configuration
training:
  # Basic training parameters
  # Epochs - 当前设置为3，论文建议可调节为1,3,10,30,100
  num_train_epochs: 3
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  gradient_accumulation_steps: 4
  
  # Learning rate and optimization
  # Learning rate - 当前2.0e-5，论文建议可调节为3e-5, 1e-5, 3e-6
  learning_rate: 3.0e-5

  # Weight decay - 当前1.0e-6，可调节为0.0或0.1*learning_rate
  weight_decay: 1.0e-6
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1.0e-8
  max_grad_norm: 1.0
  
  # Warmup and scheduling
  warmup_steps: 2
  warmup_ratio: 0.0
  lr_scheduler_type: "linear"
  
  # Mixed precision and optimization
  bf16: true
  fp16: false
  optim: "paged_adamw_8bit"

  # Label Smoothing Configuration
  # 标签平滑技术，用于提高模型泛化能力和减少过拟合
  # 论文推荐值：0.0, 0.1, 0.3
  label_smoothing_factor: 0.1
  
  # Logging and evaluation
  logging_steps: 100
  eval_steps: 500
  eval_strategy: "no"  # Options: no, steps, epoch
  
  # Saving configuration
  save_strategy: "steps"  # Options: no, steps, epoch
  save_steps: 1000
  save_total_limit: 1
  load_best_model_at_end: false
  
  # Output and reporting
  output_dir: "/home/<USER>/johnny_ws/models/finetuned/paligemma-3b-448-721/checkpoints"
  final_model_dir: "/home/<USER>/johnny_ws/models/finetuned/paligemma-3b-448-721/final_model"
  report_to: ["tensorboard"]  # Options: tensorboard, wandb, none
  
  # Memory and performance
  dataloader_pin_memory: false
  dataloader_num_workers: 0
  remove_unused_columns: false
  
  # Gradient checkpointing for memory efficiency
  gradient_checkpointing: false
  
  # Resume training
  resume_from_checkpoint: null  # Path to checkpoint or null

# Environment Configuration
environment:
  hf_endpoint: "https://hf-mirror.com"
  seed: 42
  
# Hardware Configuration
hardware:
  # Memory management
  max_memory_mb: null  # null for auto, or specify in MB
  
  # Multi-GPU settings (if applicable)
  local_rank: -1
  
# Logging Configuration
logging:
  level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR
  log_file: null  # null for console only, or specify file path
