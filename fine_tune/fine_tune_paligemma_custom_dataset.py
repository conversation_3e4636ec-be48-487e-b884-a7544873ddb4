"""
PaliGemma Fine-tuning with Custom Dataset
Based on official parameters from Fine_tune_PaliGemma.ipynb
Author: Johnny
"""

import torch
import json
import os
import logging
from typing import Dict, Any
from torch.utils.data import Dataset
from transformers import (
    PaliGemmaProcessor,
    PaliGemmaForConditionalGeneration,
    Trainer,
    TrainingArguments
)
from peft import get_peft_model, LoraConfig
from PIL import Image

try:
    import yaml
except ImportError:
    raise ImportError("PyYAML is required. Install it with: pip install pyyaml")

def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file {config_path} not found")
    except yaml.YAMLError as e:
        raise ValueError(f"Error parsing YAML configuration: {e}")

def setup_environment(config: Dict[str, Any]):
    """Setup environment variables and logging"""
    # Set HuggingFace endpoint
    if config.get('environment', {}).get('hf_endpoint'):
        os.environ["HF_ENDPOINT"] = config['environment']['hf_endpoint']

    # Set random seed
    seed = config.get('environment', {}).get('seed', 42)
    torch.manual_seed(seed)

    # Setup logging
    log_level = config.get('logging', {}).get('level', 'INFO')
    log_file = config.get('logging', {}).get('log_file')

    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(levelname)s - %(message)s',
        filename=log_file
    )

    return logging.getLogger(__name__)

def validate_config(config: Dict[str, Any]) -> None:
    """Validate configuration parameters"""
    logger = logging.getLogger(__name__)

    # Validate label smoothing factor
    training_config = config.get('training', {})
    label_smoothing = training_config.get('label_smoothing_factor', 0.0)
    if not 0.0 <= label_smoothing <= 1.0:
        raise ValueError(f"label_smoothing_factor must be between 0.0 and 1.0, got {label_smoothing}")

    # Validate LLM dropout parameters
    model_config = config.get('model', {})
    llm_dropout = model_config.get('llm_dropout', {})

    attention_dropout = llm_dropout.get('attention_dropout')
    if attention_dropout is not None and not 0.0 <= attention_dropout <= 1.0:
        raise ValueError(f"attention_dropout must be between 0.0 and 1.0, got {attention_dropout}")

    hidden_dropout = llm_dropout.get('hidden_dropout')
    if hidden_dropout is not None and not 0.0 <= hidden_dropout <= 1.0:
        raise ValueError(f"hidden_dropout must be between 0.0 and 1.0, got {hidden_dropout}")

    # Validate freeze_vit parameter
    freeze_vit = model_config.get('freeze_vit', False)
    if not isinstance(freeze_vit, bool):
        raise ValueError(f"freeze_vit must be a boolean, got {type(freeze_vit)}")

    # Log configuration summary
    logger.info("Configuration validation passed:")
    logger.info(f"  - Label smoothing factor: {label_smoothing}")
    logger.info(f"  - Attention dropout: {attention_dropout}")
    logger.info(f"  - Hidden dropout: {hidden_dropout}")
    logger.info(f"  - Freeze ViT: {freeze_vit}")

class JSONDataset(Dataset):
    """Custom dataset class for JSONL format data"""
    def __init__(self, jsonl_file_path: str, image_directory_path: str, config: Dict[str, Any]):
        self.jsonl_file_path = jsonl_file_path
        self.image_directory_path = image_directory_path
        self.config = config
        self.entries = self._load_entries()

        # Image transforms from config - Note: processor will handle resizing
        # We only need RGB conversion here, processor handles the final resize
        self.resize_dims = config.get('dataset', {}).get('image_transform', {}).get('resize', [224, 224])
        self.transform = None  # Let processor handle all transformations
    
    def _load_entries(self):
        entries = []
        with open(self.jsonl_file_path, 'r') as file:
            for line in file:
                data = json.loads(line)
                entries.append(data)
        return entries
    
    def __len__(self):
        return len(self.entries)
    
    def __getitem__(self, idx: int):
        if idx < 0 or idx >= len(self.entries):
            raise IndexError("Index out of range")

        entry = self.entries[idx]
        image_path = os.path.join(self.image_directory_path, entry['image'])
        image = Image.open(image_path)

        # Convert to RGB if specified in config
        if self.config.get('dataset', {}).get('image_transform', {}).get('convert_rgb', True):
            image = image.convert("RGB")

        # Note: Processor will handle resizing to the correct resolution
        # The resize_dims from config will be used by processor
        return {
            'image': image,
            'prefix': entry.get('prefix', ''),
            'suffix': entry.get('suffix', ''),
            'image_name': entry['image']
        }

def create_collate_fn(processor, device, dtype=torch.bfloat16):
    """Create a collate function with processor and device"""
    def collate_fn(batch):
        """Custom collate function following official format"""
        images = [item['image'] for item in batch]
        prefixes = ["<image>" + item['prefix'] for item in batch]
        suffixes = [item['suffix'] for item in batch]

        # Process using official method
        inputs = processor(
            text=prefixes,
            images=images,
            suffix=suffixes,
            return_tensors="pt",
            padding="longest"
        )

        # Convert to appropriate dtype and device
        inputs = inputs.to(dtype).to(device)
        return inputs

    return collate_fn

def setup_model_and_processor(config: Dict[str, Any], device: str):
    """Setup model and processor with configuration"""
    # Get model configuration
    model_config = config.get('model', {})
    model_id = model_config.get('model_id', "/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224")

    # Get torch dtype
    dtype_str = model_config.get('torch_dtype', 'bfloat16')
    torch_dtype = getattr(torch, dtype_str)

    # Load processor
    processor = PaliGemmaProcessor.from_pretrained(model_id)

    # Configure processor with custom resolution if specified
    dataset_config = config.get('dataset', {})
    image_transform_config = dataset_config.get('image_transform', {})
    resize_dims = image_transform_config.get('resize', [224, 224])

    # Update processor's image processor size if different from default
    if hasattr(processor, 'image_processor') and hasattr(processor.image_processor, 'size'):
        if isinstance(resize_dims, list) and len(resize_dims) == 2:
            processor.image_processor.size = {"height": resize_dims[0], "width": resize_dims[1]}
            print(f"Updated processor image size to: {resize_dims[0]}x{resize_dims[1]}")
        else:
            print(f"Warning: Invalid resize dimensions: {resize_dims}, using default")

    # Load model with configuration settings
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        model_id,
        torch_dtype=torch_dtype
    ).to(device)

    # Apply dropout configuration to language model if specified
    llm_config = model_config.get('llm_dropout', {})
    if llm_config:
        attention_dropout = llm_config.get('attention_dropout')
        hidden_dropout = llm_config.get('hidden_dropout')

        # Try to access language model layers with proper error handling
        try:
            # For PaliGemma2, the correct path is model.language_model.layers
            if hasattr(model, 'language_model') and hasattr(model.language_model, 'layers'):
                layers = model.language_model.layers
                print(f"Found {len(layers)} language model layers")

                for layer in layers:
                    # Apply attention dropout
                    if attention_dropout is not None:
                        if hasattr(layer, 'self_attn'):
                            # For Gemma2, attention dropout might be in different locations
                            if hasattr(layer.self_attn, 'attention_dropout'):
                                layer.self_attn.attention_dropout = attention_dropout
                            elif hasattr(layer.self_attn, 'dropout'):
                                layer.self_attn.dropout = attention_dropout

                    # Apply hidden dropout (for MLP layers)
                    if hidden_dropout is not None:
                        if hasattr(layer, 'mlp'):
                            if hasattr(layer.mlp, 'dropout'):
                                layer.mlp.dropout = hidden_dropout

                if attention_dropout is not None:
                    print(f"Applied attention dropout: {attention_dropout}")
                if hidden_dropout is not None:
                    print(f"Applied hidden dropout: {hidden_dropout}")
            else:
                print("Warning: Could not find language model layers for dropout configuration")
                print(f"Available attributes in language_model: {dir(model.language_model) if hasattr(model, 'language_model') else 'No language_model'}")

        except Exception as e:
            print(f"Warning: Failed to apply LLM dropout configuration: {e}")
            print("Continuing without LLM dropout modifications...")

    # Apply vision encoder freezing if specified
    freeze_vit = model_config.get('freeze_vit', False)
    if freeze_vit:
        # Freeze vision tower parameters
        if hasattr(model, 'vision_tower'):
            model.vision_tower.eval()
            for param in model.vision_tower.parameters():
                param.requires_grad = False
            print("Vision encoder (ViT) parameters frozen")
        else:
            print("Warning: vision_tower not found in model, cannot freeze ViT")

    # Apply LoRA configuration from config
    lora_config_dict = config.get('lora', {})
    lora_config = LoraConfig(
        r=lora_config_dict.get('r', 8),
        lora_alpha=lora_config_dict.get('lora_alpha', 32),
        target_modules=lora_config_dict.get('target_modules', [
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj"
        ]),
        task_type=lora_config_dict.get('task_type', "CAUSAL_LM"),
        lora_dropout=lora_config_dict.get('lora_dropout', 0.1)
    )

    model = get_peft_model(model, lora_config)

    return model, processor

def main(config_path: str = "config.yaml"):
    global device, processor

    # Load configuration
    config = load_config(config_path)

    # Validate configuration
    validate_config(config)

    # Setup environment and logging
    logger = setup_environment(config)
    logger.info("Starting PaliGemma fine-tuning")

    # Setup device
    device_config = config.get('model', {}).get('device', 'auto')
    if device_config == 'auto':
        device = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        device = device_config

    logger.info(f"Using device: {device}")

    # Setup model and processor
    model, processor = setup_model_and_processor(config, device)

    # Load datasets from config
    dataset_config = config.get('dataset', {})
    train_config = dataset_config.get('train', {})

    train_dataset = JSONDataset(
        jsonl_file_path=train_config.get('jsonl_file_path', "/path/to/your/train/annotations.jsonl"),
        image_directory_path=train_config.get('image_directory_path', "/path/to/your/train/images"),
        config=config
    )

    # Optional: validation dataset
    eval_dataset = None
    validation_config = dataset_config.get('validation', {})
    if validation_config.get('enabled', False):
        eval_dataset = JSONDataset(
            jsonl_file_path=validation_config.get('jsonl_file_path'),
            image_directory_path=validation_config.get('image_directory_path'),
            config=config
        )
        logger.info("Validation dataset loaded")

    # Training arguments from configuration
    training_config = config.get('training', {})

    training_args = TrainingArguments(
        # Basic training parameters
        num_train_epochs=training_config.get('num_train_epochs', 2),
        per_device_train_batch_size=training_config.get('per_device_train_batch_size', 1),
        per_device_eval_batch_size=training_config.get('per_device_eval_batch_size', 1),
        gradient_accumulation_steps=training_config.get('gradient_accumulation_steps', 4),

        # Learning rate and optimization
        learning_rate=training_config.get('learning_rate', 2e-5),
        weight_decay=training_config.get('weight_decay', 1e-6),
        adam_beta1=training_config.get('adam_beta1', 0.9),
        adam_beta2=training_config.get('adam_beta2', 0.999),
        adam_epsilon=training_config.get('adam_epsilon', 1e-8),
        max_grad_norm=training_config.get('max_grad_norm', 1.0),

        # Warmup and scheduling
        warmup_steps=training_config.get('warmup_steps', 2),
        warmup_ratio=training_config.get('warmup_ratio', 0.0),
        lr_scheduler_type=training_config.get('lr_scheduler_type', 'linear'),

        # Mixed precision and optimization
        bf16=training_config.get('bf16', True),
        fp16=training_config.get('fp16', False),
        optim=training_config.get('optim', 'paged_adamw_8bit'),

        # Label smoothing for better generalization
        label_smoothing_factor=training_config.get('label_smoothing_factor', 0.0),

        # Logging and evaluation
        logging_steps=training_config.get('logging_steps', 100),
        eval_steps=training_config.get('eval_steps', 500),
        eval_strategy=training_config.get('eval_strategy', 'no'),

        # Saving configuration
        save_strategy=training_config.get('save_strategy', 'steps'),
        save_steps=training_config.get('save_steps', 1000),
        save_total_limit=training_config.get('save_total_limit', 1),
        load_best_model_at_end=training_config.get('load_best_model_at_end', False),

        # Output and reporting
        output_dir=training_config.get('output_dir', 'paligemma_custom_ft'),
        report_to=training_config.get('report_to', ['tensorboard']),

        # Memory and performance
        remove_unused_columns=training_config.get('remove_unused_columns', False),
        dataloader_pin_memory=training_config.get('dataloader_pin_memory', False),
        dataloader_num_workers=training_config.get('dataloader_num_workers', 0),
        gradient_checkpointing=training_config.get('gradient_checkpointing', False),

        # Resume training
        resume_from_checkpoint=training_config.get('resume_from_checkpoint')
    )

    # Create collate function with processor and device
    dtype_str = config.get('model', {}).get('torch_dtype', 'bfloat16')
    torch_dtype = getattr(torch, dtype_str)
    collate_fn = create_collate_fn(processor, device, torch_dtype)

    # Initialize trainer
    trainer = Trainer(
        model=model,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset if eval_dataset else None,
        data_collator=collate_fn,
        args=training_args
    )

    # Start training
    logger.info("Starting training...")
    trainer.train(resume_from_checkpoint=training_config.get('resume_from_checkpoint'))

    # Save model
    final_model_dir = training_config.get('final_model_dir', 'paligemma_custom_finetuned')
    logger.info(f"Saving model to {final_model_dir}...")
    trainer.save_model(final_model_dir)

    logger.info("Training completed!")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Fine-tune PaliGemma with custom dataset')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file (default: config.yaml)')

    args = parser.parse_args()
    main(args.config)