# PaliGemma Fine-tuning with Configuration

This directory contains a configurable PaliGemma fine-tuning script that allows you to customize all training parameters through a YAML configuration file.

## Files

- `fine_tune_paligemma_custom_dataset.py` - Main fine-tuning script
- `config.yaml` - Default configuration file with all available parameters
- `example_usage.py` - Script to generate example configurations
- `README.md` - This documentation

## Quick Start

1. **Install dependencies** (if not already installed):
   ```bash
   pip install torch transformers peft pillow torchvision pyyaml
   ```

2. **Update the configuration**:
   - Edit `config.yaml` to set your dataset paths and training parameters
   - Or create a custom config file using `example_usage.py`

3. **Run the training**:
   ```bash
   # Using default config.yaml
   python fine_tune_paligemma_custom_dataset.py
   
   # Using custom config file
   python fine_tune_paligemma_custom_dataset.py --config my_config.yaml
   ```

## Configuration Structure

### Model Configuration
```yaml
model:
  model_id: "/path/to/paligemma/model"  # Path to your PaliGemma model
  torch_dtype: "bfloat16"               # Data type: float16, bfloat16, float32
  device: "auto"                        # Device: auto, cuda, cpu
```

### LoRA Configuration
```yaml
lora:
  r: 8                                  # LoRA rank
  lora_alpha: 32                        # LoRA alpha parameter
  target_modules:                       # Modules to apply LoRA to
    - "q_proj"
    - "k_proj"
    # ... more modules
  task_type: "CAUSAL_LM"               # Task type
  lora_dropout: 0.1                    # LoRA dropout rate
```

### Dataset Configuration
```yaml
dataset:
  train:
    jsonl_file_path: "/path/to/train.jsonl"
    image_directory_path: "/path/to/train/images"
  
  validation:
    enabled: false                      # Enable validation dataset
    jsonl_file_path: "/path/to/valid.jsonl"
    image_directory_path: "/path/to/valid/images"
  
  image_transform:
    resize: [224, 224]                  # Image resize dimensions
    convert_rgb: true                   # Convert images to RGB
```

### Training Configuration
```yaml
training:
  # Basic parameters
  num_train_epochs: 2
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 4
  
  # Learning rate and optimization
  learning_rate: 2.0e-5
  weight_decay: 1.0e-6
  optim: "paged_adamw_8bit"
  
  # Mixed precision
  bf16: true
  fp16: false
  
  # Logging and saving
  logging_steps: 100
  save_strategy: "steps"
  save_steps: 1000
  output_dir: "paligemma_custom_ft"
  final_model_dir: "paligemma_custom_finetuned"
  
  # Resume training (optional)
  resume_from_checkpoint: null
```

## Dataset Format

Your JSONL file should contain entries like:
```json
{"image": "image1.jpg", "prefix": "What is in this image?", "suffix": "A cat sitting on a chair."}
{"image": "image2.jpg", "prefix": "Describe the scene:", "suffix": "A beautiful sunset over mountains."}
```

## Advanced Usage

### Custom Configuration
Create a custom configuration file:
```bash
python example_usage.py
# This creates 'my_config.yaml' with example settings
```

### Resume Training
To resume from a checkpoint, set in your config:
```yaml
training:
  resume_from_checkpoint: "/path/to/checkpoint"
```

### Validation Dataset
Enable validation by setting:
```yaml
dataset:
  validation:
    enabled: true
    jsonl_file_path: "/path/to/validation.jsonl"
    image_directory_path: "/path/to/validation/images"

training:
  evaluation_strategy: "steps"  # or "epoch"
  eval_steps: 500
```

### Memory Optimization
For limited GPU memory:
```yaml
training:
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 8  # Increase this
  gradient_checkpointing: true    # Enable gradient checkpointing
  dataloader_pin_memory: false
```

### Logging
Configure logging level and output:
```yaml
logging:
  level: "INFO"                 # DEBUG, INFO, WARNING, ERROR
  log_file: "training.log"      # null for console only
```

## Troubleshooting

1. **Out of Memory**: Reduce `per_device_train_batch_size` or increase `gradient_accumulation_steps`
2. **Model not found**: Check the `model_id` path in your configuration
3. **Dataset errors**: Verify your JSONL file format and image paths
4. **Configuration errors**: Validate your YAML syntax

## Example Commands

```bash
# Basic training with default config
python fine_tune_paligemma_custom_dataset.py

# Training with custom config
python fine_tune_paligemma_custom_dataset.py --config my_custom_config.yaml

# Generate example config
python example_usage.py
```
