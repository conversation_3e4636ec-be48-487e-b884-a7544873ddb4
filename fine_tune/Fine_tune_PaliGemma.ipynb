{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/merveenoyan/smol-vision/blob/main/Fine_tune_PaliGemma.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "m8t6tkjuuONX"}, "source": ["## <PERSON><PERSON><PERSON><PERSON><PERSON> Fine-tuning\n", "\n", "In this notebook, we will fine-tune [pretrained PaliGemma](https://huggingface.co/google/paligemma2-3b-pt-448) on a small split of [VQAv2](https://huggingface.co/datasets/HuggingFaceM4/VQAv2) dataset. Let's get started by installing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EB0gv8OzHfLV", "outputId": "9de07e75-ddf4-4347-fc41-432a23774e2c"}, "outputs": [], "source": ["!pip install -q -U datasets bitsandbytes peft git+https://github.com/huggingface/transformers.git"]}, {"cell_type": "markdown", "metadata": {"id": "q_85okyYt1eo"}, "source": ["We will authenticate to access the model using `notebook_login()`."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 17, "referenced_widgets": ["4f0e85aa740146d3aca81588a0288031", "c7fcb9dd46e649c4b8bd967b69bdb867", "c3fad0f1cb954317a20ee158f7e10363", "3deca9286f89422aa691325b39347b0b", "ca1c290bfb654f1190bbde68d51167f1", "2d8493a60b7a42c1b25ec0bbe0a59043", "c25efe32ee7c40d3a4c95093abb2a720", "55c01e2c04d1499ca5b9b19dea7e4e02", "bf9da831d7ad4651a262c5e7f80bbf87", "ed2d3d1a700143d2a48e9a9b13bd1200", "40782cfc43a8437da5534feee03c6ba6", "b6fac3155dd140bc8e1b010270bc3cc2", "ca348c721475417582ed5018ed43151f", "3f07afac7c194db7a16167d177562a46", "5515d96f0c8947f0ad4b7f17eb7d63f6", "d703de12cf9d4f87aa6ec2cc52f1090a", "757bc788bd6842d28a9f889187ffb88e", "65f10d2456cb4ee1963fac050e4c34f7", "9335e48fe8ba4fe9b535b5ece1be6ff5", "80df5f3cd6c646808b09d99daed5bfd2"]}, "id": "NzJZSHD8tZZy", "outputId": "c01b2b6f-3c1e-45da-9fc0-f4f518bcca24"}, "outputs": [], "source": ["from huggingface_hub import notebook_login\n", "notebook_login()"]}, {"cell_type": "markdown", "metadata": {"id": "9_jUBDTEuw1j"}, "source": ["Let's load the dataset."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "az5kdSbNpjgH", "outputId": "2d9f379c-eb31-45b0-b84c-79c2a2577d01"}, "outputs": [], "source": ["from datasets import load_dataset\n", "ds = load_dataset('merve/vqav2-small', split=\"validation\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "wN1c9Aqhqt47"}, "outputs": [], "source": ["split_ds = ds.train_test_split(test_size=0.9) # we'll use a very small split for demo\n", "train_ds = split_ds[\"test\"]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TNJW2ty4yy4L", "outputId": "f76414b2-8f37-48ae-d369-b977323fa892"}, "outputs": [], "source": ["train_ds"]}, {"cell_type": "markdown", "metadata": {"id": "Hi_Y1blXwA04"}, "source": ["Our dataset is a very general one and similar to many datasets that <PERSON><PERSON><PERSON><PERSON><PERSON> was trained with. In this case, we do not need to fine-tune the image encoder, the multimodal projector but we will only fine-tune the text decoder."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "Zya_PWM3uBWs"}, "outputs": [], "source": ["from transformers import PaliGemmaProcessor\n", "model_id =\"/home/<USER>/johnny_ws/models/paligemma2-3b-pt-224\" # or your favorite PaliGemma"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["8458933373264dbeb58d0b5ace4fd9c6", "714009484da745dc8a87e5066b939de2", "e43e970ce8ba477e83081a4c7fea05f5", "7138aa9537fc4b4f809e57665be87139", "46810cc7c7c54e31a65e609c386d86d9", "cfed7deef0b74f4b9d160e9fdc2b138e", "23ddab24ac304751b3babfaeec9360eb", "79e87175ffb949bd8cddf4577210a42d", "5aed84a20ac34f2b943d26d66decc88f", "3ca0e1427ac6477c9921929af7ff00d1", "a9a5503caf384b93bf987e5271a577d2"]}, "id": "iZRvrfUquH1y", "outputId": "34f12289-6ef4-49d9-9257-ad0328961190"}, "outputs": [], "source": ["from transformers import PaliGemmaForConditionalGeneration\n", "import torch\n", "device = \"cuda\"\n", "model = PaliGemmaForConditionalGeneration.from_pretrained(model_id, torch_dtype=torch.bfloat16).to(device)\n", "\n", "for param in model.vision_tower.parameters():\n", "    param.requires_grad = False\n", "\n", "for param in model.multi_modal_projector.parameters():\n", "    param.requires_grad = False\n"]}, {"cell_type": "markdown", "metadata": {"id": "uCiVI-xUwSJm"}, "source": ["Alternatively, if you want to do LoRA and QLoRA fine-tuning, you can run below cells to load the adapter either in full precision or quantized."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 66, "referenced_widgets": ["c68f0fe7a6bb4060afcb05e3f6422288", "fef3c94897fc4ffa86f91aac7a45ac7f", "92881d2e3f1a438b92a389cc6022f7ad", "f518ab021bc648f188638fd168879edd", "1a29c71234d74f08b2645f9383fee126", "f8553ec713ea440eb0208a1012547988", "25e0373512b747ba8ebe020b8b8ab932", "daff4ba27c68441395aa5377111f30f1", "863090b3318e4e0186bd46d3d1479de4", "acae1751ff5d4293bb588c2d9c7ab851", "8859eb8d9c154cb79a302db1568768fa"]}, "id": "9AYeuyzNuJ9X", "outputId": "aaedd707-f694-4ba8-ba43-7ae2a3739e73"}, "outputs": [], "source": ["from transformers import BitsAndBytesConfig, PaliGemmaForConditionalGeneration\n", "from peft import get_peft_model, LoraConfig\n", "\n", "bnb_config = BitsAndBytesConfig(load_in_4bit=True, bnb_4bit_compute_dtype=torch.bfloat16)\n", "\n", "lora_config = LoraConfig(\n", "    r=8,\n", "    target_modules=[\"q_proj\", \"o_proj\", \"k_proj\", \"v_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", "    task_type=\"CAUSAL_LM\",\n", ")\n", "\n", "model = PaliGemmaForConditionalGeneration.from_pretrained(model_id, device_map=\"auto\")#, quantization_config=bnb_config)\n", "model = get_peft_model(model, lora_config)\n", "model.print_trainable_parameters()\n", "#trainable params: 11,298,816 || all params: 2,934,634,224 || trainable%: 0.38501616002417344\n"]}, {"cell_type": "markdown", "metadata": {"id": "sfxtN1iKRWXX"}, "source": ["We need to take tokens to same dtype as model so need to store it as a variable."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "uGZ6FnioRWEc"}, "outputs": [], "source": ["DTYPE = model.dtype"]}, {"cell_type": "markdown", "metadata": {"id": "OsquATWQu2lJ"}, "source": ["Load the processor to preprocess the dataset."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "wQ_gbnXARKz1"}, "outputs": [], "source": ["processor = PaliGemmaProcessor.from_pretrained(model_id)"]}, {"cell_type": "markdown", "metadata": {"id": "QZROnV-pu7rt"}, "source": ["We will preprocess our examples. We need to prepare a prompt template and pass the text input inside, pass it with batches of images to processor. Then we will set the pad tokens and image tokens to -100 to let the model ignore them. We will pass our preprocessed input as labels to make the model learn how to generate responses."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "hdw3uBcNuGmw"}, "outputs": [], "source": ["import torch\n", "\n", "image_token = processor.tokenizer.convert_tokens_to_ids(\"<image>\")\n", "def collate_fn(examples):\n", "  texts = [\"<image>answer en \" + example[\"question\"] for example in examples]\n", "  labels= [example['multiple_choice_answer'] for example in examples]\n", "  images = [example[\"image\"].convert(\"RGB\") for example in examples]\n", "  tokens = processor(text=texts, images=images, suffix=labels,\n", "                    return_tensors=\"pt\", padding=\"longest\")\n", "\n", "  tokens = tokens.to(DTYPE).to(device)\n", "  return tokens\n"]}, {"cell_type": "markdown", "metadata": {"id": "logv0oLqwbIe"}, "source": ["We will now initialize the `TrainingArguments`."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "Il7zKQO9uMPT"}, "outputs": [], "source": ["from transformers import TrainingArguments\n", "args=TrainingArguments(\n", "            num_train_epochs=2,\n", "            remove_unused_columns=False,\n", "            per_device_train_batch_size=1,\n", "            gradient_accumulation_steps=4,\n", "            warmup_steps=2,\n", "            learning_rate=2e-5,\n", "            weight_decay=1e-6,\n", "            adam_beta2=0.999,\n", "            logging_steps=100,\n", "            optim=\"paged_adamw_8bit\", # you can use paged optimizers like paged_adamw_8bit for QLoRA\n", "            save_strategy=\"steps\",\n", "            save_steps=1000,\n", "            save_total_limit=1,\n", "            output_dir=\"paligemma_vqav2\",\n", "            bf16=True,\n", "            report_to=[\"tensorboard\"],\n", "            dataloader_pin_memory=False\n", "        )\n"]}, {"cell_type": "markdown", "metadata": {"id": "8pR0EaGlwrDp"}, "source": ["We can now start training."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "CguCGDv1uNkF"}, "outputs": [], "source": ["from transformers import Trainer\n", "\n", "trainer = Trainer(\n", "        model=model,\n", "        train_dataset=train_ds ,\n", "        data_collator=collate_fn,\n", "        args=args\n", "        )\n"]}, {"cell_type": "markdown", "metadata": {"id": "ZX912_liP-Eh"}, "source": ["LoRA with bsz of 2 works on A100 Colab. You can apply gradient accumulation (which is enabled in this notebook) to simulate larger batch sizes.\n", "Currently there's an issue with QLoRA, we are investigating and will solve soon."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "9KFPQLrnF2Ha"}, "outputs": [], "source": ["trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "O9fMDEjXSSzF"}, "outputs": [], "source": ["trainer.push_to_hub()"]}, {"cell_type": "markdown", "metadata": {"id": "JohfxEJQjLBd"}, "source": ["You can find steps to infer [here](https://colab.research.google.com/drive/100IQcvMvGm9y--oelbLfI__eHCoz5Ser?usp=sharing)."]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "A100", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "smolvision", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"1a29c71234d74f08b2645f9383fee126": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "23ddab24ac304751b3babfaeec9360eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25e0373512b747ba8ebe020b8b8ab932": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2d8493a60b7a42c1b25ec0bbe0a59043": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d703de12cf9d4f87aa6ec2cc52f1090a", "placeholder": "​", "style": "IPY_MODEL_757bc788bd6842d28a9f889187ffb88e", "value": "\n<b>Pro Tip:</b> If you don't already have one, you can create a dedicated\n'notebooks' token with 'write' access, that you can then easily reuse for all\nnotebooks. </center>"}}, "3ca0e1427ac6477c9921929af7ff00d1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3deca9286f89422aa691325b39347b0b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "CheckboxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "CheckboxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "CheckboxView", "description": "Add token as git credential?", "description_tooltip": null, "disabled": false, "indent": true, "layout": "IPY_MODEL_b6fac3155dd140bc8e1b010270bc3cc2", "style": "IPY_MODEL_ca348c721475417582ed5018ed43151f", "value": true}}, "3f07afac7c194db7a16167d177562a46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40782cfc43a8437da5534feee03c6ba6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "46810cc7c7c54e31a65e609c386d86d9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f0e85aa740146d3aca81588a0288031": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "VBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": [], "layout": "IPY_MODEL_c25efe32ee7c40d3a4c95093abb2a720"}}, "5515d96f0c8947f0ad4b7f17eb7d63f6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ButtonStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_color": null, "font_weight": ""}}, "55c01e2c04d1499ca5b9b19dea7e4e02": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5aed84a20ac34f2b943d26d66decc88f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "65f10d2456cb4ee1963fac050e4c34f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "LabelModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "LabelModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "LabelView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9335e48fe8ba4fe9b535b5ece1be6ff5", "placeholder": "​", "style": "IPY_MODEL_80df5f3cd6c646808b09d99daed5bfd2", "value": "Connecting..."}}, "7138aa9537fc4b4f809e57665be87139": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3ca0e1427ac6477c9921929af7ff00d1", "placeholder": "​", "style": "IPY_MODEL_a9a5503caf384b93bf987e5271a577d2", "value": " 2/2 [00:00&lt;00:00,  2.83it/s]"}}, "714009484da745dc8a87e5066b939de2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cfed7deef0b74f4b9d160e9fdc2b138e", "placeholder": "​", "style": "IPY_MODEL_23ddab24ac304751b3babfaeec9360eb", "value": "Loading checkpoint shards: 100%"}}, "757bc788bd6842d28a9f889187ffb88e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "79e87175ffb949bd8cddf4577210a42d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80df5f3cd6c646808b09d99daed5bfd2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8458933373264dbeb58d0b5ace4fd9c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_714009484da745dc8a87e5066b939de2", "IPY_MODEL_e43e970ce8ba477e83081a4c7fea05f5", "IPY_MODEL_7138aa9537fc4b4f809e57665be87139"], "layout": "IPY_MODEL_46810cc7c7c54e31a65e609c386d86d9"}}, "863090b3318e4e0186bd46d3d1479de4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8859eb8d9c154cb79a302db1568768fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "92881d2e3f1a438b92a389cc6022f7ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_daff4ba27c68441395aa5377111f30f1", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_863090b3318e4e0186bd46d3d1479de4", "value": 2}}, "9335e48fe8ba4fe9b535b5ece1be6ff5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a9a5503caf384b93bf987e5271a577d2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "acae1751ff5d4293bb588c2d9c7ab851": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b6fac3155dd140bc8e1b010270bc3cc2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf9da831d7ad4651a262c5e7f80bbf87": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c25efe32ee7c40d3a4c95093abb2a720": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": "center", "align_self": null, "border": null, "bottom": null, "display": "flex", "flex": null, "flex_flow": "column", "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "50%"}}, "c3fad0f1cb954317a20ee158f7e10363": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "PasswordModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "PasswordModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "PasswordView", "continuous_update": true, "description": "Token:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_ed2d3d1a700143d2a48e9a9b13bd1200", "placeholder": "​", "style": "IPY_MODEL_40782cfc43a8437da5534feee03c6ba6", "value": ""}}, "c68f0fe7a6bb4060afcb05e3f6422288": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fef3c94897fc4ffa86f91aac7a45ac7f", "IPY_MODEL_92881d2e3f1a438b92a389cc6022f7ad", "IPY_MODEL_f518ab021bc648f188638fd168879edd"], "layout": "IPY_MODEL_1a29c71234d74f08b2645f9383fee126"}}, "c7fcb9dd46e649c4b8bd967b69bdb867": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_55c01e2c04d1499ca5b9b19dea7e4e02", "placeholder": "​", "style": "IPY_MODEL_bf9da831d7ad4651a262c5e7f80bbf87", "value": "<center> <img\nsrc=https://huggingface.co/front/assets/huggingface_logo-noborder.svg\nalt='Hugging Face'> <br> Copy a token from <a\nhref=\"https://huggingface.co/settings/tokens\" target=\"_blank\">your Hugging Face\ntokens page</a> and paste it below. <br> Immediately click login after copying\nyour token or it might be stored in plain text in this notebook file. </center>"}}, "ca1c290bfb654f1190bbde68d51167f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ButtonModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ButtonView", "button_style": "", "description": "<PERSON><PERSON>", "disabled": false, "icon": "", "layout": "IPY_MODEL_3f07afac7c194db7a16167d177562a46", "style": "IPY_MODEL_5515d96f0c8947f0ad4b7f17eb7d63f6", "tooltip": ""}}, "ca348c721475417582ed5018ed43151f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cfed7deef0b74f4b9d160e9fdc2b138e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d703de12cf9d4f87aa6ec2cc52f1090a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "daff4ba27c68441395aa5377111f30f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e43e970ce8ba477e83081a4c7fea05f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79e87175ffb949bd8cddf4577210a42d", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5aed84a20ac34f2b943d26d66decc88f", "value": 2}}, "ed2d3d1a700143d2a48e9a9b13bd1200": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f518ab021bc648f188638fd168879edd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_acae1751ff5d4293bb588c2d9c7ab851", "placeholder": "​", "style": "IPY_MODEL_8859eb8d9c154cb79a302db1568768fa", "value": " 2/2 [00:05&lt;00:00,  2.39s/it]"}}, "f8553ec713ea440eb0208a1012547988": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fef3c94897fc4ffa86f91aac7a45ac7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f8553ec713ea440eb0208a1012547988", "placeholder": "​", "style": "IPY_MODEL_25e0373512b747ba8ebe020b8b8ab932", "value": "Loading checkpoint shards: 100%"}}}}}, "nbformat": 4, "nbformat_minor": 0}