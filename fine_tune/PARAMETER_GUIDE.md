# PaliGemma 微调新增参数使用指南

本文档详细介绍了PaliGemma微调脚本中新增的三个重要参数：Label Smoothing、Dropout in LLM和FreezeViT。

## 1. Label Smoothing（标签平滑）

### 功能说明
标签平滑是一种正则化技术，通过软化硬标签来提高模型的泛化能力，减少过拟合。

### 参数配置
```yaml
training:
  label_smoothing_factor: 0.1  # 推荐值：0.0-0.3
```

### 参数说明
- **参数名称**: `label_smoothing_factor`
- **取值范围**: 0.0 - 1.0
- **推荐值**: 0.1
- **默认值**: 0.0（不使用标签平滑）

### 作用机制
- 0.0：不使用标签平滑，使用原始的硬标签
- 0.1：将标签从 [0, 1] 软化为 [0.1/num_classes, 1-0.1+0.1/num_classes]
- 更高的值会产生更平滑的标签分布

### 使用建议
- 对于小数据集或容易过拟合的场景，建议使用 0.1-0.2
- 对于大数据集，可以使用较小的值 0.05-0.1
- 如果模型已经表现良好，可以设置为 0.0

## 2. Dropout in LLM（语言模型Dropout）

### 功能说明
在语言模型的注意力层和隐藏层中添加dropout，防止过拟合并提高模型的鲁棒性。

### 参数配置
```yaml
model:
  llm_dropout:
    attention_dropout: 0.1    # 注意力层dropout
    hidden_dropout: 0.1       # 隐藏层dropout
```

### 参数说明
- **attention_dropout**: 注意力机制中的dropout率
  - 取值范围: 0.0 - 1.0
  - 推荐值: 0.1
  - 作用位置: 自注意力层
  
- **hidden_dropout**: 隐藏层（MLP）中的dropout率
  - 取值范围: 0.0 - 1.0
  - 推荐值: 0.1
  - 作用位置: 前馈神经网络层

### 使用建议
- 训练数据充足时，可以使用较低的dropout率（0.05-0.1）
- 训练数据较少或模型容易过拟合时，可以增加dropout率（0.1-0.3）
- 如果不需要dropout，可以设置为 0.0 或删除该配置项

## 3. FreezeViT（冻结视觉编码器）

### 功能说明
冻结视觉Transformer（ViT）的参数，只训练语言模型部分，可以减少训练参数量并提高训练稳定性。

### 参数配置
```yaml
model:
  freeze_vit: true  # true: 冻结ViT, false: 训练ViT
```

### 参数说明
- **参数名称**: `freeze_vit`
- **取值**: true 或 false
- **默认值**: false
- **作用**: 控制是否冻结视觉编码器的参数

### 使用场景
- **freeze_vit: true** 适用于：
  - 计算资源有限的情况
  - 视觉特征已经足够好，主要优化语言理解
  - 快速原型验证
  - 防止视觉编码器过拟合

- **freeze_vit: false** 适用于：
  - 有充足的计算资源
  - 需要针对特定视觉任务优化
  - 视觉和语言需要联合优化

## 4. 完整配置示例

```yaml
# Model Configuration
model:
  model_id: "/path/to/paligemma/model"
  torch_dtype: "bfloat16"
  device: "cuda"
  
  # 冻结视觉编码器
  freeze_vit: false
  
  # 语言模型dropout配置
  llm_dropout:
    attention_dropout: 0.1
    hidden_dropout: 0.1

# Training Configuration
training:
  # 基础训练参数
  num_train_epochs: 3
  learning_rate: 2.0e-5
  
  # 标签平滑
  label_smoothing_factor: 0.1
  
  # 其他参数...
```

## 5. 参数验证和测试

### 运行测试脚本
```bash
cd /path/to/fine_tune
python test_parameters.py
```

### 验证功能
测试脚本会验证：
- 参数范围的正确性
- 配置文件的完整性
- 参数生效情况

## 6. 性能调优建议

### 参数组合建议

1. **保守配置**（适合初学者）：
   ```yaml
   freeze_vit: true
   label_smoothing_factor: 0.1
   llm_dropout:
     attention_dropout: 0.1
     hidden_dropout: 0.1
   ```

2. **平衡配置**（推荐）：
   ```yaml
   freeze_vit: false
   label_smoothing_factor: 0.1
   llm_dropout:
     attention_dropout: 0.1
     hidden_dropout: 0.1
   ```

3. **激进配置**（充足资源）：
   ```yaml
   freeze_vit: false
   label_smoothing_factor: 0.05
   llm_dropout:
     attention_dropout: 0.05
     hidden_dropout: 0.05
   ```

### 调优策略
1. 先使用默认配置进行基线测试
2. 根据过拟合情况调整dropout和label smoothing
3. 根据计算资源决定是否冻结ViT
4. 通过验证集性能指导参数调整

## 7. 常见问题

### Q: 如何判断是否需要使用这些参数？
A: 观察训练过程中的验证损失，如果出现过拟合（训练损失持续下降但验证损失上升），建议启用这些正则化参数。

### Q: 参数设置错误会怎样？
A: 脚本包含参数验证功能，会在训练开始前检查参数范围，避免无效配置。

### Q: 这些参数对训练速度有影响吗？
A: FreezeViT会显著减少训练时间和内存使用；dropout对速度影响很小；label smoothing几乎无影响。

## 8. 更新日志

- 2025-01-21: 添加Label Smoothing、LLM Dropout、FreezeViT三个参数
- 包含参数验证和测试功能
- 提供详细的使用指南和调优建议
