#!/usr/bin/env python3
"""
测试脚本：验证PaliGemma微调脚本中新增参数的正确性
Author: Johnny
"""

import torch
import yaml
import sys
import os
from transformers import PaliGemmaForConditionalGeneration, PaliGemmaProcessor

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fine_tune_paligemma_custom_dataset import setup_model_and_processor, validate_config

def test_label_smoothing():
    """测试Label Smoothing参数"""
    print("=== 测试 Label Smoothing 参数 ===")
    
    # 测试配置
    test_config = {
        'training': {
            'label_smoothing_factor': 0.1
        }
    }
    
    try:
        validate_config(test_config)
        print("✓ Label smoothing 配置验证通过")
        print(f"  - 标签平滑因子: {test_config['training']['label_smoothing_factor']}")
    except Exception as e:
        print(f"✗ Label smoothing 配置验证失败: {e}")

def test_llm_dropout():
    """测试LLM Dropout参数"""
    print("\n=== 测试 LLM Dropout 参数 ===")
    
    # 测试配置
    test_config = {
        'model': {
            'llm_dropout': {
                'attention_dropout': 0.1,
                'hidden_dropout': 0.1
            }
        }
    }
    
    try:
        validate_config(test_config)
        print("✓ LLM dropout 配置验证通过")
        print(f"  - 注意力dropout: {test_config['model']['llm_dropout']['attention_dropout']}")
        print(f"  - 隐藏层dropout: {test_config['model']['llm_dropout']['hidden_dropout']}")
    except Exception as e:
        print(f"✗ LLM dropout 配置验证失败: {e}")

def test_freeze_vit():
    """测试FreezeViT参数"""
    print("\n=== 测试 FreezeViT 参数 ===")
    
    # 测试配置
    test_config = {
        'model': {
            'freeze_vit': True
        }
    }
    
    try:
        validate_config(test_config)
        print("✓ FreezeViT 配置验证通过")
        print(f"  - 冻结视觉编码器: {test_config['model']['freeze_vit']}")
    except Exception as e:
        print(f"✗ FreezeViT 配置验证失败: {e}")

def test_config_file():
    """测试完整配置文件"""
    print("\n=== 测试完整配置文件 ===")
    
    config_path = "config.yaml"
    if not os.path.exists(config_path):
        print(f"✗ 配置文件 {config_path} 不存在")
        return
    
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        validate_config(config)
        print("✓ 完整配置文件验证通过")
        
        # 显示新增参数的配置
        model_config = config.get('model', {})
        training_config = config.get('training', {})
        
        print("  新增参数配置:")
        print(f"    - Label smoothing: {training_config.get('label_smoothing_factor', 'Not set')}")
        print(f"    - Freeze ViT: {model_config.get('freeze_vit', 'Not set')}")
        
        llm_dropout = model_config.get('llm_dropout', {})
        if llm_dropout:
            print(f"    - Attention dropout: {llm_dropout.get('attention_dropout', 'Not set')}")
            print(f"    - Hidden dropout: {llm_dropout.get('hidden_dropout', 'Not set')}")
        else:
            print("    - LLM dropout: Not configured")
            
    except Exception as e:
        print(f"✗ 配置文件验证失败: {e}")

def test_parameter_ranges():
    """测试参数范围验证"""
    print("\n=== 测试参数范围验证 ===")
    
    # 测试无效的label_smoothing_factor
    invalid_configs = [
        {
            'training': {'label_smoothing_factor': -0.1},
            'name': 'negative label_smoothing_factor'
        },
        {
            'training': {'label_smoothing_factor': 1.5},
            'name': 'label_smoothing_factor > 1.0'
        },
        {
            'model': {'llm_dropout': {'attention_dropout': -0.1}},
            'name': 'negative attention_dropout'
        },
        {
            'model': {'llm_dropout': {'hidden_dropout': 1.5}},
            'name': 'hidden_dropout > 1.0'
        },
        {
            'model': {'freeze_vit': 'invalid'},
            'name': 'non-boolean freeze_vit'
        }
    ]
    
    for test_case in invalid_configs:
        try:
            validate_config(test_case)
            print(f"✗ 应该失败但通过了: {test_case['name']}")
        except ValueError:
            print(f"✓ 正确捕获无效配置: {test_case['name']}")
        except Exception as e:
            print(f"? 意外错误 {test_case['name']}: {e}")

def main():
    """主测试函数"""
    print("PaliGemma 微调参数测试")
    print("=" * 50)
    
    # 运行各项测试
    test_label_smoothing()
    test_llm_dropout()
    test_freeze_vit()
    test_config_file()
    test_parameter_ranges()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
